/*
 * YAFFS: Yet another Flash File System . A NAND-flash specific file system.
 * YAFFS: 又一个闪存文件系统 - 专为NAND闪存设计的文件系统
 *
 * Copyright (C) 2002-2018 Aleph One Ltd.
 *
 * Created by <PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU Lesser General Public License version 2.1 as
 * published by the Free Software Foundation.
 *
 * Note: Only YAFFS headers are LGPL, YAFFS C code is covered by GPL.
 */

/*
 * YAFFS1打包标签头文件 - YAFFS1 Packed Tags Header
 *
 * This is used to pack YAFFS1 tags, not YAFFS2 tags.
 * 此文件用于打包YAFFS1标签，不是YAFFS2标签。
 */

#ifndef __YAFFS_PACKEDTAGS1_H__
#define __YAFFS_PACKEDTAGS1_H__

#include "yaffs_guts.h"

/*
 * YAFFS1打包标签结构 - YAFFS1 packed tags structure
 * 用于在NAND闪存中存储YAFFS1标签信息的紧凑格式
 * Compact format for storing YAFFS1 tag information in NAND flash
 */
struct yaffs_packed_tags1 {
	u32 chunk_id:20;		/* 数据块ID (20位) - Chunk ID (20 bits) */
	u32 serial_number:2;		/* 序列号 (2位) - Serial number (2 bits) */
	u32 n_bytes:10;			/* 字节数 (10位) - Number of bytes (10 bits) */
	u32 obj_id:18;			/* 对象ID (18位) - Object ID (18 bits) */
	u32 ecc:12;			/* ECC校验码 (12位) - ECC code (12 bits) */
	u32 deleted:1;			/* 删除标志 (1位) - Deleted flag (1 bit) */
	u32 unused_stuff:1;		/* 未使用位 (1位) - Unused bit (1 bit) */
	unsigned should_be_ff;		/* 应该为0xFF的字段 - Field that should be 0xFF */
};

/* YAFFS1标签打包/解包函数 - YAFFS1 tag pack/unpack functions */
void yaffs_pack_tags1(struct yaffs_packed_tags1 *pt,			/* 将扩展标签打包为YAFFS1格式 - Pack extended tags to YAFFS1 format */
		      const struct yaffs_ext_tags *t);
void yaffs_unpack_tags1(struct yaffs_ext_tags *t,			/* 将YAFFS1格式解包为扩展标签 - Unpack YAFFS1 format to extended tags */
			const struct yaffs_packed_tags1 *pt);
#endif
