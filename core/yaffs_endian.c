/*
 * YAFFS: Yet Another Flash File System. A NAND-flash specific file system.
 * YAFFS: 又一个闪存文件系统 - 专为NAND闪存设计的文件系统
 *
 * Copyright (C) 2002-2018 Aleph One Ltd.
 *
 * Created by <PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License version 2 as
 * published by the Free Software Foundation.
 *
 * Endian processing functions.
 * 字节序处理函数。
 */

/*
 * YAFFS字节序处理实现 - YAFFS Endianness Processing Implementation
 *
 * 此文件实现了YAFFS的字节序转换功能，确保跨平台兼容性
 * This file implements YAFFS endianness conversion functionality to ensure cross-platform compatibility
 */

#include "yaffs_endian.h"
#include "yaffs_guts.h"

/*
 * 处理无符号32位整数字节序 - Handle unsigned 32-bit integer endianness
 * 根据设备配置转换32位无符号整数的字节序
 * Convert endianness of 32-bit unsigned integer based on device configuration
 */
void yaffs_do_endian_u32(struct yaffs_dev *dev, u32 *val)
{
	if (!dev->swap_endian)
		return;		/* 无需转换字节序 - No endian conversion needed */
	*val = swap_u32(*val);	/* 转换字节序 - Convert endianness */
}

/*
 * 处理有符号32位整数字节序 - Handle signed 32-bit integer endianness
 * 根据设备配置转换32位有符号整数的字节序
 * Convert endianness of 32-bit signed integer based on device configuration
 */
void yaffs_do_endian_s32(struct yaffs_dev *dev, s32 *val)
{
	if (!dev->swap_endian)
		return;		/* 无需转换字节序 - No endian conversion needed */
	*val = swap_s32(*val);	/* 转换字节序 - Convert endianness */
}

/*
 * 处理对象头字节序 - Handle object header endianness
 * 转换对象头结构中所有字段的字节序
 * Convert endianness of all fields in object header structure
 */
void yaffs_do_endian_oh(struct yaffs_dev *dev, struct yaffs_obj_hdr *oh)
{
	if (!dev->swap_endian)
		return;		/* 无需转换字节序 - No endian conversion needed */

	/* 转换每个字段 - Change every field */
	oh->type = swap_u32(oh->type);				/* 对象类型 - Object type */
	oh->parent_obj_id = swap_u32(oh->parent_obj_id);	/* 父对象ID - Parent object ID */

	oh->yst_mode = swap_u32(oh->yst_mode);			/* 文件模式 - File mode */

	/* 用户和组信息 - User and group information */
	oh->yst_uid = swap_u32(oh->yst_uid);			/* 用户ID - User ID */
	oh->yst_gid = swap_u32(oh->yst_gid);			/* 组ID - Group ID */

	/* 时间戳 - Timestamps */
	oh->yst_atime = swap_u32(oh->yst_atime);		/* 访问时间 - Access time */
	oh->yst_mtime = swap_u32(oh->yst_mtime);		/* 修改时间 - Modification time */
	oh->yst_ctime = swap_u32(oh->yst_ctime);		/* 创建时间 - Creation time */

	oh->file_size_low = swap_u32(oh->file_size_low);	/* 文件大小低32位 - Low 32 bits of file size */

	oh->equiv_id = swap_u32(oh->equiv_id);			/* 等价ID - Equivalent ID */

	oh->yst_rdev = swap_u32(oh->yst_rdev);			/* 设备号 - Device number */

	/* Windows时间戳/64位时间 - Windows timestamps / 64-bit times */
	oh->win_ctime[0] = swap_u32(oh->win_ctime[0]);		/* Windows创建时间低位 - Windows creation time low */
	oh->win_ctime[1] = swap_u32(oh->win_ctime[1]);		/* Windows创建时间高位 - Windows creation time high */
	oh->win_atime[0] = swap_u32(oh->win_atime[0]);		/* Windows访问时间低位 - Windows access time low */
	oh->win_atime[1] = swap_u32(oh->win_atime[1]);		/* Windows访问时间高位 - Windows access time high */
	oh->win_mtime[0] = swap_u32(oh->win_mtime[0]);		/* Windows修改时间低位 - Windows modification time low */
	oh->win_mtime[1] = swap_u32(oh->win_mtime[1]);		/* Windows修改时间高位 - Windows modification time high */

	/* 带内标签字段 - Inband tag fields */
	oh->inband_shadowed_obj_id = swap_u32(oh->inband_shadowed_obj_id);	/* 带内影射对象ID - Inband shadowed object ID */
	oh->inband_is_shrink = swap_u32(oh->inband_is_shrink);			/* 带内收缩标志 - Inband shrink flag */

	oh->file_size_high = swap_u32(oh->file_size_high);	/* 文件大小高32位 - High 32 bits of file size */
	oh->reserved[0] = swap_u32(oh->reserved[0]);		/* 保留字段 - Reserved field */
	oh->shadows_obj = swap_s32(oh->shadows_obj);		/* 影射对象 - Shadows object */

	oh->is_shrink = swap_u32(oh->is_shrink);		/* 收缩标志 - Shrink flag */
}


/*
 * 处理打包标签2字节序 - Handle packed tags2 endianness
 * 转换YAFFS2打包标签结构中所有字段的字节序
 * Convert endianness of all fields in YAFFS2 packed tags structure
 */
void yaffs_do_endian_packed_tags2(struct yaffs_dev *dev,
				struct yaffs_packed_tags2_tags_only *ptt)
{
	if (!dev->swap_endian)
		return;		/* 无需转换字节序 - No endian conversion needed */

	ptt->seq_number = swap_u32(ptt->seq_number);	/* 序列号 - Sequence number */
	ptt->obj_id = swap_u32(ptt->obj_id);		/* 对象ID - Object ID */
	ptt->chunk_id = swap_u32(ptt->chunk_id);	/* 数据块ID - Chunk ID */
	ptt->n_bytes = swap_u32(ptt->n_bytes);		/* 字节数 - Number of bytes */
}

/*
 * 配置设备字节序 - Configure device endianness
 * 根据存储的字节序和当前CPU字节序确定是否需要字节序转换
 * Determine if endian conversion is needed based on stored endianness and current CPU endianness
 */
void yaffs_endian_config(struct yaffs_dev *dev)
{
	u32 x = 1;	/* 用于检测CPU字节序的测试值 - Test value for detecting CPU endianness */

	if (dev->tnode_size < 1)
		BUG();	/* Tnode大小必须有效 - Tnode size must be valid */

	dev->swap_endian = 0;	/* 默认不需要字节序转换 - Default no endian conversion */

	if (((char *)&x)[0] == 1) {
		/* 小端序CPU - Little Endian CPU */
		if (dev->param.stored_endian == 2 /* big endian */)
			dev->swap_endian = 1;	/* 存储为大端序，需要转换 - Stored as big endian, need conversion */
	} else  {
		/* 大端序CPU - Big Endian CPU */
		if (dev->param.stored_endian == 1 /* little endian */)
			dev->swap_endian = 1;	/* 存储为小端序，需要转换 - Stored as little endian, need conversion */
	}

	/* 如果需要字节序转换，分配Tnode交换缓冲区 - If endian conversion needed, allocate Tnode swap buffer */
	if (dev->swap_endian)
		dev->tn_swap_buffer = kmalloc(dev->tnode_size, GFP_NOFS);
}
