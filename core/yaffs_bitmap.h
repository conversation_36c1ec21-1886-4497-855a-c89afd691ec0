/*
 * YAFFS: Yet another Flash File System . A NAND-flash specific file system.
 * YAFFS: 又一个闪存文件系统 - 专为NAND闪存设计的文件系统
 *
 * Copyright (C) 2002-2018 Aleph One Ltd.
 *
 * Created by <PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU Lesser General Public License version 2.1 as
 * published by the Free Software Foundation.
 *
 * Note: Only YAFFS headers are LGPL, YAFFS C code is covered by GPL.
 */

/*
 * YAFFS位图操作头文件 - YAFFS Bitmap Operations Header
 *
 * Chunk bitmap manipulations
 * 数据块位图操作功能
 */

#ifndef __YAFFS_BITMAP_H__
#define __YAFFS_BITMAP_H__

#include "yaffs_guts.h"

/* 位图操作函数 - Bitmap operation functions */
void yaffs_verify_chunk_bit_id(struct yaffs_dev *dev, int blk, int chunk);	/* 验证数据块位ID - Verify chunk bit ID */
void yaffs_clear_chunk_bits(struct yaffs_dev *dev, int blk);			/* 清除块中所有数据块位 - Clear all chunk bits in block */
void yaffs_clear_chunk_bit(struct yaffs_dev *dev, int blk, int chunk);		/* 清除指定数据块位 - Clear specific chunk bit */
void yaffs_set_chunk_bit(struct yaffs_dev *dev, int blk, int chunk);		/* 设置指定数据块位 - Set specific chunk bit */
int yaffs_check_chunk_bit(struct yaffs_dev *dev, int blk, int chunk);		/* 检查指定数据块位 - Check specific chunk bit */
int yaffs_still_some_chunks(struct yaffs_dev *dev, int blk);			/* 检查块中是否还有数据块 - Check if block still has some chunks */
int yaffs_count_chunk_bits(struct yaffs_dev *dev, int blk);			/* 统计块中设置的数据块位数 - Count set chunk bits in block */

#endif
