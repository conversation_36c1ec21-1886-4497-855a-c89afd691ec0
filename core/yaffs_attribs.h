/*
 * YAFFS: Yet another Flash File System . A NAND-flash specific file system.
 * YAFFS: 又一个闪存文件系统 - 专为NAND闪存设计的文件系统
 *
 * Copyright (C) 2002-2018 Aleph One Ltd.
 *
 * Created by <PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU Lesser General Public License version 2.1 as
 * published by the Free Software Foundation.
 *
 * Note: Only YAFFS headers are LGPL, YAFFS C code is covered by GPL.
 */

/*
 * YAFFS属性处理头文件 - YAFFS Attributes Handling Header
 *
 * 此模块负责处理文件和目录的属性信息，如权限、时间戳等
 * This module handles attribute information for files and directories, such as permissions, timestamps, etc.
 */

#ifndef __YAFFS_ATTRIBS_H__
#define __YAFFS_ATTRIBS_H__

#include "yaffs_guts.h"

/* 属性加载函数 - Attribute loading functions */
void yaffs_load_attribs(struct yaffs_obj *obj, struct yaffs_obj_hdr *oh);	/* 从对象头加载属性到对象 - Load attributes from object header to object */
void yaffs_load_attribs_oh(struct yaffs_obj_hdr *oh, struct yaffs_obj *obj);	/* 从对象加载属性到对象头 - Load attributes from object to object header */

/* 属性初始化和时间处理函数 - Attribute initialization and time handling functions */
void yaffs_attribs_init(struct yaffs_obj *obj, u32 gid, u32 uid, u32 rdev);	/* 初始化对象属性 - Initialize object attributes */
void yaffs_load_current_time(struct yaffs_obj *obj, int do_a, int do_c);	/* 加载当前时间到对象 - Load current time to object */

/* 属性设置和获取函数 - Attribute set/get functions */
int yaffs_set_attribs(struct yaffs_obj *obj, struct iattr *attr);		/* 设置对象属性 - Set object attributes */
int yaffs_get_attribs(struct yaffs_obj *obj, struct iattr *attr);		/* 获取对象属性 - Get object attributes */

#endif
