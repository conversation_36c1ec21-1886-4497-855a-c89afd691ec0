/*
 * YAFFS: Yet Another Flash File System. A NAND-flash specific file system.
 * YAFFS: 又一个闪存文件系统 - 专为NAND闪存设计的文件系统
 *
 * Copyright (C) 2002-2018 Aleph One Ltd.
 *
 * Created by <PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License version 2 as
 * published by the Free Software Foundation.
 */

/*
 * YAFFS摘要管理实现 - YAFFS Summary Management Implementation
 *
 * Summaries write the useful part of the tags for the chunks in a block into an
 * an array which is written to the last n chunks of the block.
 * Reading the summaries gives all the tags for the block in one read. Much
 * faster.
 *
 * Chunks holding summaries are marked with tags making it look like
 * they are part of a fake file.
 *
 * The summary could also be used during gc.
 *
 * 摘要将块中数据块标签的有用部分写入数组，
 * 该数组写入块的最后n个数据块中。
 * 读取摘要可以一次读取获得块的所有标签，速度更快。
 *
 * 包含摘要的数据块用标签标记，使其看起来像虚拟文件的一部分。
 * 摘要也可以在垃圾回收期间使用。
 */

#include "yaffs_summary.h"
#include "yaffs_packedtags2.h"
#include "yaffs_nand.h"
#include "yaffs_getblockinfo.h"
#include "yaffs_bitmap.h"

/*
 * 摘要数据结构说明 - Summary data structure description
 * The summary is built up in an array of summary tags.
 * This gets written to the last one or two (maybe more) chunks in a block.
 * A summary header is written as the first part of each chunk of summary data.
 * The summary header must match or the summary is rejected.
 *
 * 摘要由摘要标签数组构建。
 * 这被写入块的最后一个或两个（可能更多）数据块中。
 * 摘要头作为每个摘要数据块的第一部分写入。
 * 摘要头必须匹配，否则摘要被拒绝。
 */

/*
 * 摘要标签结构 - Summary tags structure
 * Summary tags don't need the sequence number because that is redundant.
 * 摘要标签不需要序列号，因为那是冗余的。
 */
struct yaffs_summary_tags {
	unsigned obj_id;	/* 对象ID - Object ID */
	unsigned chunk_id;	/* 数据块ID - Chunk ID */
	unsigned n_bytes;	/* 字节数 - Number of bytes */
};

/*
 * 摘要头结构 - Summary header structure
 * 用于验证摘要数据的有效性
 * Used to verify the validity of summary data
 */
struct yaffs_summary_header {
	unsigned version;	/* 版本号，必须匹配当前版本 - Version, must match current version */
	unsigned block;		/* 块号，必须是此块 - Block number, must be this block */
	unsigned seq;		/* 序列号，必须是此序列号 - Sequence number, must be this sequence number */
	unsigned sum;		/* 校验和，标签中所有字节的总和 - Checksum, sum of all bytes in tags */
};


/*
 * 清除摘要数据 - Clear summary data
 * 将摘要标签数组清零
 * Clear the summary tags array to zero
 */
static void yaffs_summary_clear(struct yaffs_dev *dev)
{
	if (!dev->sum_tags)
		return;	/* 如果摘要标签不存在则返回 - Return if summary tags don't exist */

	memset(dev->sum_tags, 0, dev->chunks_per_summary *
		sizeof(struct yaffs_summary_tags));	/* 清零摘要标签数组 - Clear summary tags array */
}

/*
 * 清理摘要系统 - Deinitialize summary system
 * 释放摘要相关的内存资源
 * Free memory resources related to summary
 */
void yaffs_summary_deinit(struct yaffs_dev *dev)
{
	kfree(dev->sum_tags);		/* 释放摘要标签内存 - Free summary tags memory */
	dev->sum_tags = NULL;
	kfree(dev->gc_sum_tags);	/* 释放垃圾回收摘要标签内存 - Free GC summary tags memory */
	dev->gc_sum_tags = NULL;
	dev->chunks_per_summary = 0;	/* 重置每个摘要的数据块数 - Reset chunks per summary */
}

/*
 * 初始化摘要系统 - Initialize summary system
 * 分配摘要相关的内存资源并计算参数
 * Allocate memory resources for summary and calculate parameters
 */
int yaffs_summary_init(struct yaffs_dev *dev)
{
	int sum_bytes;			/* 摘要字节数 - Summary bytes */
	int chunks_used;		/* 摘要使用的数据块数 - Number of chunks used by summary */
	int sum_tags_bytes;		/* 摘要标签字节数 - Summary tags bytes */

	/* 计算摘要所需的总字节数 - Calculate total bytes needed for summary */
	sum_bytes = dev->param.chunks_per_block *
			sizeof(struct yaffs_summary_tags);

	chunks_used = (sum_bytes + dev->data_bytes_per_chunk - 1)/
			(dev->data_bytes_per_chunk -
				sizeof(struct yaffs_summary_header));

	dev->chunks_per_summary = dev->param.chunks_per_block - chunks_used;
	sum_tags_bytes = sizeof(struct yaffs_summary_tags) *
				dev->chunks_per_summary;
	dev->sum_tags = kmalloc(sum_tags_bytes, GFP_NOFS);
	dev->gc_sum_tags = kmalloc(sum_tags_bytes, GFP_NOFS);
	if (!dev->sum_tags || !dev->gc_sum_tags) {
		yaffs_summary_deinit(dev);
		return YAFFS_FAIL;
	}

	yaffs_summary_clear(dev);

	return YAFFS_OK;
}

static unsigned yaffs_summary_sum(struct yaffs_dev *dev)
{
	u8 *sum_buffer = (u8 *)dev->sum_tags;
	int i;
	unsigned sum = 0;

	i = sizeof(struct yaffs_summary_tags) *
				dev->chunks_per_summary;
	while (i > 0) {
		sum += *sum_buffer;
		sum_buffer++;
		i--;
	}

	return sum;
}

static int yaffs_summary_write(struct yaffs_dev *dev, int blk)
{
	struct yaffs_ext_tags tags;
	u8 *buffer;
	u8 *sum_buffer = (u8 *)dev->sum_tags;
	int n_bytes;
	int chunk_in_nand;
	int chunk_in_block;
	int result;
	int this_tx;
	struct yaffs_summary_header hdr;
	int sum_bytes_per_chunk = dev->data_bytes_per_chunk - sizeof(hdr);
	struct yaffs_block_info *bi = yaffs_get_block_info(dev, blk);

	buffer = yaffs_get_temp_buffer(dev);
	n_bytes = sizeof(struct yaffs_summary_tags) *
				dev->chunks_per_summary;
	memset(&tags, 0, sizeof(struct yaffs_ext_tags));
	tags.obj_id = YAFFS_OBJECTID_SUMMARY;
	tags.chunk_id = 1;
	chunk_in_block = dev->chunks_per_summary;
	chunk_in_nand = dev->alloc_block * dev->param.chunks_per_block +
						dev->chunks_per_summary;
	hdr.version = YAFFS_SUMMARY_VERSION;
	hdr.block = blk;
	hdr.seq = bi->seq_number;
	hdr.sum = yaffs_summary_sum(dev);

	do {
		this_tx = n_bytes;
		if (this_tx > sum_bytes_per_chunk)
			this_tx = sum_bytes_per_chunk;
		memcpy(buffer, &hdr, sizeof(hdr));
		memcpy(buffer + sizeof(hdr), sum_buffer, this_tx);
		tags.n_bytes = this_tx + sizeof(hdr);
		result = yaffs_wr_chunk_tags_nand(dev, chunk_in_nand,
						buffer, &tags);

		if (result != YAFFS_OK)
			break;
		yaffs_set_chunk_bit(dev, blk, chunk_in_block);
		bi->pages_in_use++;
		dev->n_free_chunks--;

		n_bytes -= this_tx;
		sum_buffer += this_tx;
		chunk_in_nand++;
		chunk_in_block++;
		tags.chunk_id++;
	} while (result == YAFFS_OK && n_bytes > 0);
	yaffs_release_temp_buffer(dev, buffer);


	if (result == YAFFS_OK)
		bi->has_summary = 1;


	return result;
}

int yaffs_summary_read(struct yaffs_dev *dev,
			struct yaffs_summary_tags *st,
			int blk)
{
	struct yaffs_ext_tags tags;
	u8 *buffer;
	u8 *sum_buffer = (u8 *)st;
	int n_bytes;
	u32 chunk_id;
	int chunk_in_nand;
	int chunk_in_block;
	int result;
	int this_tx;
	struct yaffs_summary_header hdr;
	struct yaffs_block_info *bi = yaffs_get_block_info(dev, blk);
	int sum_bytes_per_chunk = dev->data_bytes_per_chunk - sizeof(hdr);

	buffer = yaffs_get_temp_buffer(dev);
	n_bytes = sizeof(struct yaffs_summary_tags) * dev->chunks_per_summary;
	chunk_in_block = dev->chunks_per_summary;
	chunk_in_nand = blk * dev->param.chunks_per_block +
							dev->chunks_per_summary;
	chunk_id = 1;
	do {
		this_tx = n_bytes;
		if (this_tx > sum_bytes_per_chunk)
			this_tx = sum_bytes_per_chunk;
		result = yaffs_rd_chunk_tags_nand(dev, chunk_in_nand,
						buffer, &tags);

		if (tags.chunk_id != chunk_id ||
			tags.obj_id != YAFFS_OBJECTID_SUMMARY ||
			tags.chunk_used == 0 ||
			tags.ecc_result > YAFFS_ECC_RESULT_FIXED ||
			tags.n_bytes != (this_tx + sizeof(hdr)))
				result = YAFFS_FAIL;
		if (result != YAFFS_OK)
			break;

		if (st == dev->sum_tags) {
			/* If we're scanning then update the block info */
			yaffs_set_chunk_bit(dev, blk, chunk_in_block);
			bi->pages_in_use++;
		}
		memcpy(&hdr, buffer, sizeof(hdr));
		memcpy(sum_buffer, buffer + sizeof(hdr), this_tx);
		n_bytes -= this_tx;
		sum_buffer += this_tx;
		chunk_in_nand++;
		chunk_in_block++;
		chunk_id++;
	} while (result == YAFFS_OK && n_bytes > 0);
	yaffs_release_temp_buffer(dev, buffer);

	if (result == YAFFS_OK) {
		/* Verify header */
		if (hdr.version != YAFFS_SUMMARY_VERSION ||
		    hdr.seq != bi->seq_number ||
		    hdr.sum != yaffs_summary_sum(dev))
			result = YAFFS_FAIL;
	}

	if (st == dev->sum_tags && result == YAFFS_OK)
		bi->has_summary = 1;

	return result;
}

int yaffs_summary_add(struct yaffs_dev *dev,
			struct yaffs_ext_tags *tags,
			int chunk_in_nand)
{
	struct yaffs_packed_tags2_tags_only tags_only;
	struct yaffs_summary_tags *sum_tags;
	int block_in_nand = chunk_in_nand / dev->param.chunks_per_block;
	int chunk_in_block = chunk_in_nand % dev->param.chunks_per_block;

	if (!dev->sum_tags)
		return YAFFS_OK;

	if (chunk_in_block >= 0 && chunk_in_block < dev->chunks_per_summary) {
		yaffs_pack_tags2_tags_only(dev, &tags_only, tags);
		sum_tags = &dev->sum_tags[chunk_in_block];

		sum_tags->chunk_id = tags_only.chunk_id;
		sum_tags->n_bytes = tags_only.n_bytes;
		sum_tags->obj_id = tags_only.obj_id;

		if (chunk_in_block == dev->chunks_per_summary - 1) {
			/* Time to write out the summary */
			yaffs_summary_write(dev, block_in_nand);
			yaffs_summary_clear(dev);
			yaffs_skip_rest_of_block(dev);
		}
	}
	return YAFFS_OK;
}

int yaffs_summary_fetch(struct yaffs_dev *dev,
			struct yaffs_ext_tags *tags,
			int chunk_in_block)
{
	struct yaffs_packed_tags2_tags_only tags_only;
	struct yaffs_summary_tags *sum_tags;
	if (chunk_in_block >= 0 && chunk_in_block < dev->chunks_per_summary) {
		sum_tags = &dev->sum_tags[chunk_in_block];
		tags_only.chunk_id = sum_tags->chunk_id;
		tags_only.n_bytes = sum_tags->n_bytes;
		tags_only.obj_id = sum_tags->obj_id;
		yaffs_unpack_tags2_tags_only(dev, tags, &tags_only);
		return YAFFS_OK;
	}
	return YAFFS_FAIL;
}

void yaffs_summary_gc(struct yaffs_dev *dev, int blk)
{
	struct yaffs_block_info *bi = yaffs_get_block_info(dev, blk);
	u32 i;

	if (!bi->has_summary)
		return;

	for (i = dev->chunks_per_summary;
	     i < dev->param.chunks_per_block;
	     i++) {
		if (yaffs_check_chunk_bit(dev, blk, i)) {
			yaffs_clear_chunk_bit(dev, blk, i);
			bi->pages_in_use--;
			dev->n_free_chunks++;
		}
	}
}
