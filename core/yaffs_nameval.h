/*
 * YAFFS: Yet another Flash File System . A NAND-flash specific file system.
 * YAFFS: 又一个闪存文件系统 - 专为NAND闪存设计的文件系统
 *
 * Copyright (C) 2002-2018 Aleph One Ltd.
 *
 * Created by <PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU Lesser General Public License version 2.1 as
 * published by the Free Software Foundation.
 *
 * Note: Only YAFFS headers are LGPL, YAFFS C code is covered by GPL.
 */

/*
 * YAFFS名值对处理头文件 - YAFFS Name-Value Pair Handling Header
 *
 * 此模块提供扩展属性的名值对存储和检索功能
 * This module provides name-value pair storage and retrieval functionality for extended attributes
 */

#ifndef __NAMEVAL_H__
#define __NAMEVAL_H__

#include "yportenv.h"

struct yaffs_dev;

/* 名值对操作函数 - Name-value pair operation functions */
int nval_del(struct yaffs_dev *dev, char *xb, int xb_size, const YCHAR * name);	/* 删除名值对 - Delete name-value pair */
int nval_set(struct yaffs_dev *dev,						/* 设置名值对 - Set name-value pair */
	     char *xb, int xb_size, const YCHAR * name, const char *buf,
	     int bsize, int flags);
int nval_get(struct yaffs_dev *dev,						/* 获取名值对 - Get name-value pair */
	     const char *xb, int xb_size, const YCHAR * name, char *buf,
	     int bsize);
int nval_list(struct yaffs_dev *dev,						/* 列出所有名值对 - List all name-value pairs */
	      const char *xb, int xb_size, char *buf, int bsize);
int nval_hasvalues(struct yaffs_dev *dev, const char *xb, int xb_size);		/* 检查是否有名值对 - Check if has name-value pairs */
#endif
