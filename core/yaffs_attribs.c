/*
 * YAFFS: Yet Another Flash File System. A NAND-flash specific file system.
 * YAFFS: 又一个闪存文件系统 - 专为NAND闪存设计的文件系统
 *
 * Copyright (C) 2002-2018 Aleph One Ltd.
 *
 * Created by <PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License version 2 as
 * published by the Free Software Foundation.
 */

/*
 * YAFFS属性处理实现 - YAFFS Attributes Handling Implementation
 *
 * 此文件实现了文件和目录属性的处理功能，包括权限、时间戳等
 * This file implements handling of file and directory attributes including permissions, timestamps, etc.
 */

#include "yaffs_guts.h"
#include "yaffs_attribs.h"

/* Linux内核版本兼容性定义 - Linux kernel version compatibility definitions */
#if (LINUX_VERSION_CODE < KERNEL_VERSION(3, 14, 0))
#define IATTR_UID ia_uid	/* 旧版本内核UID字段 - Old kernel UID field */
#define IATTR_GID ia_gid	/* 旧版本内核GID字段 - Old kernel GID field */
#else
#define IATTR_UID ia_uid.val	/* 新版本内核UID字段 - New kernel UID field */
#define IATTR_GID ia_gid.val	/* 新版本内核GID字段 - New kernel GID field */
#endif

/*
 * 从对象头加载属性到对象 - Load attributes from object header to object
 * Loading attribs from/to object header assumes the object header
 * is in cpu endian.
 * 从对象头加载属性假设对象头是CPU字节序
 */
void yaffs_load_attribs(struct yaffs_obj *obj, struct yaffs_obj_hdr *oh)
{
	obj->yst_uid = oh->yst_uid;		/* 加载用户ID - Load user ID */
	obj->yst_gid = oh->yst_gid;		/* 加载组ID - Load group ID */

	/* 加载时间戳 - Load timestamps */
	obj->yst_ctime = yaffs_oh_ctime_fetch(oh);	/* 创建时间 - Creation time */
	obj->yst_mtime = yaffs_oh_mtime_fetch(oh);	/* 修改时间 - Modification time */
	obj->yst_atime = yaffs_oh_atime_fetch(oh);	/* 访问时间 - Access time */

	obj->yst_rdev = oh->yst_rdev;		/* 设备号 - Device number */
}

/*
 * 从对象加载属性到对象头 - Load attributes from object to object header
 * 将对象的属性信息复制到对象头中
 * Copy object's attribute information to object header
 */
void yaffs_load_attribs_oh(struct yaffs_obj_hdr *oh, struct yaffs_obj *obj)
{
	oh->yst_uid = obj->yst_uid;		/* 设置用户ID - Set user ID */
	oh->yst_gid = obj->yst_gid;		/* 设置组ID - Set group ID */

	/* 设置时间戳 - Set timestamps */
	yaffs_oh_ctime_load(obj, oh);		/* 创建时间 - Creation time */
	yaffs_oh_mtime_load(obj, oh);		/* 修改时间 - Modification time */
	yaffs_oh_atime_load(obj, oh);		/* 访问时间 - Access time */

	oh->yst_rdev = obj->yst_rdev;		/* 设备号 - Device number */
}

/*
 * 加载当前时间到对象 - Load current time to object
 * 设置对象的时间戳为当前时间
 * Set object timestamps to current time
 */
void yaffs_load_current_time(struct yaffs_obj *obj, int do_a, int do_c)
{
	obj->yst_mtime = Y_CURRENT_TIME;	/* 设置修改时间为当前时间 - Set modification time to current */
	if (do_a)
		obj->yst_atime = obj->yst_mtime;	/* 如果需要，设置访问时间 - Set access time if needed */
	if (do_c)
		obj->yst_ctime = obj->yst_mtime;	/* 如果需要，设置创建时间 - Set creation time if needed */
}

/*
 * 初始化对象属性 - Initialize object attributes
 * 设置对象的基本属性信息
 * Set basic attribute information for object
 */
void yaffs_attribs_init(struct yaffs_obj *obj, u32 gid, u32 uid, u32 rdev)
{
	yaffs_load_current_time(obj, 1, 1);	/* 设置所有时间戳为当前时间 - Set all timestamps to current time */
	obj->yst_rdev = rdev;			/* 设置设备号 - Set device number */
	obj->yst_uid = uid;			/* 设置用户ID - Set user ID */
	obj->yst_gid = gid;			/* 设置组ID - Set group ID */
}

static loff_t yaffs_get_file_size(struct yaffs_obj *obj)
{
	YCHAR *alias = NULL;
	obj = yaffs_get_equivalent_obj(obj);

	switch (obj->variant_type) {
	case YAFFS_OBJECT_TYPE_FILE:
		return obj->variant.file_variant.file_size;
	case YAFFS_OBJECT_TYPE_SYMLINK:
		alias = obj->variant.symlink_variant.alias;
		if (!alias)
			return 0;
		return strnlen(alias, YAFFS_MAX_ALIAS_LENGTH);
	default:
		return 0;
	}
}

int yaffs_set_attribs(struct yaffs_obj *obj, struct iattr *attr)
{
	unsigned int valid = attr->ia_valid;

	if (valid & ATTR_MODE)
		obj->yst_mode = attr->ia_mode;
	if (valid & ATTR_UID)
		obj->yst_uid = attr->IATTR_UID;
	if (valid & ATTR_GID)
		obj->yst_gid = attr->IATTR_GID;

	if (valid & ATTR_ATIME)
		obj->yst_atime = Y_TIME_CONVERT(attr->ia_atime);
	if (valid & ATTR_CTIME)
		obj->yst_ctime = Y_TIME_CONVERT(attr->ia_ctime);
	if (valid & ATTR_MTIME)
		obj->yst_mtime = Y_TIME_CONVERT(attr->ia_mtime);

	if (valid & ATTR_SIZE)
		yaffs_resize_file(obj, attr->ia_size);

	yaffs_update_oh(obj, NULL, 1, 0, 0, NULL);

	return YAFFS_OK;
}

int yaffs_get_attribs(struct yaffs_obj *obj, struct iattr *attr)
{
	unsigned int valid = 0;

	attr->ia_mode = obj->yst_mode;
	valid |= ATTR_MODE;
	attr->IATTR_UID = obj->yst_uid;
	valid |= ATTR_UID;
	attr->IATTR_GID = obj->yst_gid;
	valid |= ATTR_GID;

	Y_TIME_CONVERT(attr->ia_atime) = obj->yst_atime;
	valid |= ATTR_ATIME;
	Y_TIME_CONVERT(attr->ia_ctime) = obj->yst_ctime;
	valid |= ATTR_CTIME;
	Y_TIME_CONVERT(attr->ia_mtime) = obj->yst_mtime;
	valid |= ATTR_MTIME;

	attr->ia_size = yaffs_get_file_size(obj);
	valid |= ATTR_SIZE;

	attr->ia_valid = valid;

	return YAFFS_OK;
}
