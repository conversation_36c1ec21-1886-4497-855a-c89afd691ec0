/*
 * YAFFS: Yet another Flash File System . A NAND-flash specific file system.
 * YAFFS: 又一个闪存文件系统 - 专为NAND闪存设计的文件系统
 *
 * Copyright (C) 2002-2018 Aleph One Ltd.
 *
 * Created by <PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU Lesser General Public License version 2.1 as
 * published by the Free Software Foundation.
 *
 * Note: Only YAFFS headers are LGPL, YAFFS C code is covered by GPL.
 */

/*
 * YAFFS1特定功能头文件 - YAFFS1 Specific Functions Header
 *
 * 此模块包含YAFFS1版本特定的功能实现
 * This module contains YAFFS1 version-specific functionality
 */

#ifndef __YAFFS_YAFFS1_H__
#define __YAFFS_YAFFS1_H__

#include "yaffs_guts.h"

/* YAFFS1扫描函数 - YAFFS1 scan function */
int yaffs1_scan(struct yaffs_dev *dev);		/* 扫描YAFFS1文件系统，重建内存中的文件系统结构 -
						 * Scan YAFFS1 filesystem and rebuild in-memory filesystem structure
						 */

#endif
