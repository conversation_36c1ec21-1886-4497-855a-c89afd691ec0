/*
 * YAFFS: Yet another Flash File System . A NAND-flash specific file system.
 * YAFFS: 又一个闪存文件系统 - 专为NAND闪存设计的文件系统
 *
 * Copyright (C) 2002-2018 Aleph One Ltd.
 *
 * Created by <PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU Lesser General Public License version 2.1 as
 * published by the Free Software Foundation.
 *
 * Note: Only YAFFS headers are LGPL, YAFFS C code is covered by GPL.
 */

/*
 * YAFFS NAND接口头文件 - YAFFS NAND Interface Header
 *
 * 此模块提供YAFFS与NAND闪存硬件交互的接口函数
 * This module provides interface functions for YAFFS to interact with NAND flash hardware
 */

#ifndef __YAFFS_NAND_H__
#define __YAFFS_NAND_H__
#include "yaffs_guts.h"

/* NAND读写操作函数 - NAND read/write operation functions */
int yaffs_rd_chunk_tags_nand(struct yaffs_dev *dev, int nand_chunk,		/* 从NAND读取数据块和标签 - Read chunk and tags from NAND */
			     u8 *buffer, struct yaffs_ext_tags *tags);

int yaffs_wr_chunk_tags_nand(struct yaffs_dev *dev,				/* 向NAND写入数据块和标签 - Write chunk and tags to NAND */
			     int nand_chunk,
			     const u8 *buffer, struct yaffs_ext_tags *tags);

/* NAND块管理函数 - NAND block management functions */
int yaffs_mark_bad(struct yaffs_dev *dev, int block_no);			/* 标记坏块 - Mark bad block */

int yaffs_query_init_block_state(struct yaffs_dev *dev,			/* 查询初始块状态 - Query initial block state */
				 int block_no,
				 enum yaffs_block_state *state,
				 unsigned *seq_number);

int yaffs_erase_block(struct yaffs_dev *dev, int flash_block);			/* 擦除闪存块 - Erase flash block */

/* NAND初始化/清理函数 - NAND init/deinit functions */
int yaffs_init_nand(struct yaffs_dev *dev);					/* 初始化NAND接口 - Initialize NAND interface */
int yaffs_deinit_nand(struct yaffs_dev *dev);					/* 清理NAND接口 - Deinitialize NAND interface */

#endif
