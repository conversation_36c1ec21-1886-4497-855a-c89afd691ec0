/*
 * YAFFS: Yet another Flash File System . A NAND-flash specific file system.
 * YAFFS: 又一个闪存文件系统 - 专为NAND闪存设计的文件系统
 *
 * Copyright (C) 2002-2018 Aleph One Ltd.
 *
 * Created by <PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU Lesser General Public License version 2.1 as
 * published by the Free Software Foundation.
 *
 * Note: Only YAFFS headers are LGPL, YAFFS C code is covered by GPL.
 */

/*
 * YAFFS2打包标签头文件 - YAFFS2 Packed Tags Header
 *
 * This is used to pack YAFFS2 tags, not YAFFS1 tags.
 * 此文件用于打包YAFFS2标签，不是YAFFS1标签。
 */

#ifndef __YAFFS_PACKEDTAGS2_H__
#define __YAFFS_PACKEDTAGS2_H__

#include "yaffs_guts.h"
#include "yaffs_ecc.h"

/*
 * YAFFS2仅标签打包结构 - YAFFS2 tags-only packed structure
 * 仅包含标签信息，不包含ECC，用于带内标签
 * Contains only tag information, no ECC, used for inband tags
 */
struct yaffs_packed_tags2_tags_only {
	unsigned seq_number;		/* 序列号 - Sequence number */
	unsigned obj_id;		/* 对象ID - Object ID */
	unsigned chunk_id;		/* 数据块ID - Chunk ID */
	unsigned n_bytes;		/* 字节数 - Number of bytes */
};

/*
 * YAFFS2完整打包标签结构 - YAFFS2 full packed tags structure
 * 包含标签信息和ECC校验码，用于OOB标签
 * Contains tag information and ECC code, used for OOB tags
 */
struct yaffs_packed_tags2 {
	struct yaffs_packed_tags2_tags_only t;	/* 标签信息 - Tag information */
	struct yaffs_ecc_other ecc;		/* ECC校验码 - ECC code */
};

/* 完整打包标签函数（带ECC），用于OOB标签 - Full packed tags with ECC, used for OOB tags */
void yaffs_pack_tags2(struct yaffs_dev *dev,				/* 将扩展标签打包为YAFFS2格式（带ECC） - Pack extended tags to YAFFS2 format with ECC */
		      struct yaffs_packed_tags2 *pt,
		      const struct yaffs_ext_tags *t, int tags_ecc);
void yaffs_unpack_tags2(struct yaffs_dev *dev,				/* 将YAFFS2格式（带ECC）解包为扩展标签 - Unpack YAFFS2 format with ECC to extended tags */
			struct yaffs_ext_tags *t, struct yaffs_packed_tags2 *pt,
			int tags_ecc);

/* 仅标签部分（无ECC），用于带内标签 - Only the tags part (no ECC) for use with inband tags */
void yaffs_pack_tags2_tags_only(struct yaffs_dev *dev,			/* 将扩展标签打包为YAFFS2仅标签格式 - Pack extended tags to YAFFS2 tags-only format */
				struct yaffs_packed_tags2_tags_only *pt,
				const struct yaffs_ext_tags *t);
void yaffs_unpack_tags2_tags_only(struct yaffs_dev *dev,		/* 将YAFFS2仅标签格式解包为扩展标签 - Unpack YAFFS2 tags-only format to extended tags */
				  struct yaffs_ext_tags *t,
				  struct yaffs_packed_tags2_tags_only *pt);
#endif
