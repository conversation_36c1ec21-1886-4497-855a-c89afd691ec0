/*
 * YAFFS: Yet Another Flash File System. A NAND-flash specific file system.
 * YAFFS: 又一个闪存文件系统 - 专为NAND闪存设计的文件系统
 *
 * Copyright (C) 2002-2018 Aleph One Ltd.
 *
 * Created by <PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License version 2 as
 * published by the Free Software Foundation.
 */

/*
 * YAFFS缓存管理头文件 - YAFFS Cache Management Header
 *
 * 此模块负责管理YAFFS的数据块缓存系统
 * This module manages YAFFS chunk cache system
 */

#ifndef __YAFFS_CACHE_H__
#define __YAFFS_CACHE_H__

#include "yaffs_guts.h"

/* 缓存状态检查函数 - Cache status check functions */
int yaffs_obj_cache_dirty(struct yaffs_obj *obj);	/* 检查对象是否有脏缓存 - Check if object has dirty cache */

/* 缓存刷新函数 - Cache flush functions */
void yaffs_flush_file_cache(struct yaffs_obj *obj, int discard);	/* 刷新文件关联的缓存，可选择丢弃或保留 - Flush file cache, discard or keep */
void yaffs_flush_whole_cache(struct yaffs_dev *dev, int discard);	/* 刷新所有缓存，可选择丢弃或保留 - Flush all cache, discard or keep */

/* 缓存获取和查找函数 - Cache grab and find functions */
struct yaffs_cache *yaffs_grab_chunk_cache(struct yaffs_dev *dev);	/* 在读写时获取缓存项 - Grab cache item during read/write */
struct yaffs_cache *yaffs_find_chunk_cache(const struct yaffs_obj *obj,	/* 查找缓存的数据块 - Find cached chunk */
						  int chunk_id);

/* 缓存使用标记函数 - Cache usage marking function */
void yaffs_use_cache(struct yaffs_dev *dev, struct yaffs_cache *cache,	/* 为LRU算法标记数据块 - Mark chunk for LRU algorithm */
			    int is_write);

/* 缓存失效函数 - Cache invalidation functions */
void yaffs_invalidate_chunk_cache(struct yaffs_obj *object,		/* 使单个缓存页失效，缓存不再保存有效数据 -
									 * Invalidate single cache page, cache no longer holds valid data
									 */
					 int chunk_id);

void yaffs_invalidate_file_cache(struct yaffs_obj *in);			/* 使与此对象关联的所有缓存页失效 -
									 * Invalidate all cache pages associated with this object
									 * 在文件删除或调整大小时执行此操作
									 * Do this whenever the file is deleted or resized
									 */

/* 缓存统计函数 - Cache statistics function */
int yaffs_count_dirty_caches(struct yaffs_dev *dev);			/* 统计脏缓存数量 - Count dirty caches */

/* 初始化/清理函数 - Init/deinit functions */
int yaffs_cache_init(struct yaffs_dev *dev);				/* 初始化缓存系统 - Initialize cache system */
void yaffs_cache_deinit(struct yaffs_dev *dev);				/* 清理缓存系统 - Deinitialize cache system */

#endif
