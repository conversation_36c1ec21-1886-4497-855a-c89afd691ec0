/*
 * YAFFS: Yet another Flash File System . A NAND-flash specific file system.
 * YAFFS: 又一个闪存文件系统 - 专为NAND闪存设计的文件系统
 *
 * Copyright (C) 2002-2018 Aleph One Ltd.
 *
 * Created by <PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU Lesser General Public License version 2.1 as
 * published by the Free Software Foundation.
 *
 * Note: Only YAFFS headers are LGPL, YAFFS C code is covered by GPL.
 */

/*
 * YAFFS调试跟踪头文件 - YAFFS Debug Tracing Header
 *
 * 此模块定义了YAFFS的调试跟踪标志和功能
 * This module defines YAFFS debug tracing flags and functionality
 */

#ifndef __YTRACE_H__
#define __YTRACE_H__

/* 全局跟踪变量 - Global tracing variables */
extern unsigned int yaffs_trace_mask;		/* 跟踪掩码，控制哪些类型的消息被输出 - Trace mask controlling which message types are output */
extern unsigned int yaffs_wr_attempts;		/* 写入尝试次数 - Number of write attempts */

/*
 * 跟踪标志定义 - Tracing flags definition
 * Tracing flags.
 * The flags masked in YAFFS_TRACE_ALWAYS are always traced.
 *
 * 在YAFFS_TRACE_ALWAYS中掩码的标志总是被跟踪
 */

/* 基础操作跟踪标志 - Basic operation tracing flags */
#define YAFFS_TRACE_OS			0x00000002	/* 操作系统接口跟踪 - OS interface tracing */
#define YAFFS_TRACE_ALLOCATE		0x00000004	/* 内存分配跟踪 - Memory allocation tracing */
#define YAFFS_TRACE_SCAN		0x00000008	/* 文件系统扫描跟踪 - Filesystem scan tracing */
#define YAFFS_TRACE_BAD_BLOCKS		0x00000010	/* 坏块处理跟踪 - Bad block handling tracing */
#define YAFFS_TRACE_ERASE		0x00000020	/* 擦除操作跟踪 - Erase operation tracing */
#define YAFFS_TRACE_GC			0x00000040	/* 垃圾回收跟踪 - Garbage collection tracing */
#define YAFFS_TRACE_WRITE		0x00000080	/* 写入操作跟踪 - Write operation tracing */
#define YAFFS_TRACE_TRACING		0x00000100	/* 跟踪系统本身跟踪 - Tracing system itself tracing */
#define YAFFS_TRACE_DELETION		0x00000200	/* 删除操作跟踪 - Deletion operation tracing */
#define YAFFS_TRACE_BUFFERS		0x00000400	/* 缓冲区操作跟踪 - Buffer operation tracing */
#define YAFFS_TRACE_NANDACCESS		0x00000800	/* NAND访问跟踪 - NAND access tracing */
#define YAFFS_TRACE_GC_DETAIL		0x00001000	/* 垃圾回收详细跟踪 - Detailed garbage collection tracing */
#define YAFFS_TRACE_SCAN_DEBUG		0x00002000	/* 扫描调试跟踪 - Scan debug tracing */
#define YAFFS_TRACE_MTD			0x00004000	/* MTD接口跟踪 - MTD interface tracing */
#define YAFFS_TRACE_CHECKPOINT		0x00008000	/* 检查点跟踪 - Checkpoint tracing */

/* 验证相关跟踪标志 - Verification related tracing flags */
#define YAFFS_TRACE_VERIFY		0x00010000	/* 基本验证跟踪 - Basic verification tracing */
#define YAFFS_TRACE_VERIFY_NAND		0x00020000	/* NAND验证跟踪 - NAND verification tracing */
#define YAFFS_TRACE_VERIFY_FULL		0x00040000	/* 完整验证跟踪 - Full verification tracing */
#define YAFFS_TRACE_VERIFY_ALL		0x000f0000	/* 所有验证跟踪 - All verification tracing */

/* 高级操作跟踪标志 - Advanced operation tracing flags */
#define YAFFS_TRACE_SYNC		0x00100000	/* 同步操作跟踪 - Sync operation tracing */
#define YAFFS_TRACE_BACKGROUND		0x00200000	/* 后台操作跟踪 - Background operation tracing */
#define YAFFS_TRACE_LOCK		0x00400000	/* 锁操作跟踪 - Lock operation tracing */
#define YAFFS_TRACE_MOUNT		0x00800000	/* 挂载操作跟踪 - Mount operation tracing */

#define YAFFS_TRACE_ERROR		0x40000000
#define YAFFS_TRACE_BUG			0x80000000
#define YAFFS_TRACE_ALWAYS		0xf0000000

#endif
