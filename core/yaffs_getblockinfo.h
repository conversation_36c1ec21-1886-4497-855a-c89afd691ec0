/*
 * YAFFS: Yet another Flash File System . A NAND-flash specific file system.
 * YAFFS: 又一个闪存文件系统 - 专为NAND闪存设计的文件系统
 *
 * Copyright (C) 2002-2018 Aleph One Ltd.
 *
 * Created by <PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU Lesser General Public License version 2.1 as
 * published by the Free Software Foundation.
 *
 * Note: Only YAFFS headers are LGPL, YAFFS C code is covered by GPL.
 */

/*
 * YAFFS块信息获取头文件 - YAFFS Block Information Retrieval Header
 *
 * 此模块提供获取块信息的内联函数
 * This module provides inline functions for retrieving block information
 */

#ifndef __YAFFS_GETBLOCKINFO_H__
#define __YAFFS_GETBLOCKINFO_H__

#include "yaffs_guts.h"
#include "yaffs_trace.h"

/*
 * 获取块信息的内联函数 - Inline function to get block information
 * Function to manipulate block info
 * 用于操作块信息的函数
 */
static inline struct yaffs_block_info *yaffs_get_block_info(struct yaffs_dev *dev, int blk)
{
	/* 检查块号是否在有效范围内 - Check if block number is within valid range */
	if (blk < (int)dev->internal_start_block ||
	    blk > (int)dev->internal_end_block) {
		yaffs_trace(YAFFS_TRACE_ERROR,
			"**>> yaffs: get_block_info block %d is not valid",
			blk);
		BUG();	/* 块号无效，触发BUG - Invalid block number, trigger BUG */
	}
	/* 返回指向块信息结构的指针 - Return pointer to block info structure */
	return &dev->block_info[blk - dev->internal_start_block];
}

#endif
