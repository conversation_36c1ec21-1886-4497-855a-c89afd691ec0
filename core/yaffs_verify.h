/*
 * YAFFS: Yet another Flash File System . A NAND-flash specific file system.
 * YAFFS: 又一个闪存文件系统 - 专为NAND闪存设计的文件系统
 *
 * Copyright (C) 2002-2018 Aleph One Ltd.
 *
 * Created by <PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU Lesser General Public License version 2.1 as
 * published by the Free Software Foundation.
 *
 * Note: Only YAFFS headers are LGPL, YAFFS C code is covered by GPL.
 */

/*
 * YAFFS验证功能头文件 - YAFFS Verification Functions Header
 *
 * 此模块提供文件系统完整性验证功能，用于调试和错误检测
 * This module provides filesystem integrity verification functionality for debugging and error detection
 */

#ifndef __YAFFS_VERIFY_H__
#define __YAFFS_VERIFY_H__

#include "yaffs_guts.h"

/* 块验证函数 - Block verification functions */
void yaffs_verify_blk(struct yaffs_dev *dev, struct yaffs_block_info *bi,	/* 验证单个块 - Verify single block */
		      int n);
void yaffs_verify_collected_blk(struct yaffs_dev *dev,				/* 验证已收集的块 - Verify collected block */
				struct yaffs_block_info *bi, int n);
void yaffs_verify_blocks(struct yaffs_dev *dev);				/* 验证所有块 - Verify all blocks */

/* 对象验证函数 - Object verification functions */
void yaffs_verify_oh(struct yaffs_obj *obj, struct yaffs_obj_hdr *oh,		/* 验证对象头 - Verify object header */
		     struct yaffs_ext_tags *tags, int parent_check);
void yaffs_verify_file(struct yaffs_obj *obj);					/* 验证文件对象 - Verify file object */
void yaffs_verify_link(struct yaffs_obj *obj);					/* 验证硬链接对象 - Verify hard link object */
void yaffs_verify_symlink(struct yaffs_obj *obj);				/* 验证符号链接对象 - Verify symbolic link object */
void yaffs_verify_special(struct yaffs_obj *obj);				/* 验证特殊文件对象 - Verify special file object */
void yaffs_verify_obj(struct yaffs_obj *obj);					/* 验证通用对象 - Verify generic object */
void yaffs_verify_objects(struct yaffs_dev *dev);				/* 验证所有对象 - Verify all objects */
void yaffs_verify_obj_in_dir(struct yaffs_obj *obj);				/* 验证目录中的对象 - Verify object in directory */
void yaffs_verify_dir(struct yaffs_obj *directory);				/* 验证目录对象 - Verify directory object */
void yaffs_verify_free_chunks(struct yaffs_dev *dev);				/* 验证空闲数据块 - Verify free chunks */

/* 文件完整性检查函数 - File integrity check functions */
int yaffs_verify_file_sane(struct yaffs_obj *obj);				/* 检查文件是否正常 - Check if file is sane */

/* 验证控制函数 - Verification control functions */
int yaffs_skip_verification(struct yaffs_dev *dev);				/* 检查是否跳过验证 - Check if verification should be skipped */

#endif
