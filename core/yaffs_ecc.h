/*
 * YAFFS: Yet another Flash File System . A NAND-flash specific file system.
 * YAFFS: 又一个闪存文件系统 - 专为NAND闪存设计的文件系统
 *
 * Copyright (C) 2002-2018 Aleph One Ltd.
 *
 * Created by <PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU Lesser General Public License version 2.1 as
 * published by the Free Software Foundation.
 *
 * Note: Only YAFFS headers are LGPL, YAFFS C code is covered by GPL.
 */

/*
 * YAFFS错误校正码(ECC)算法头文件 - YAFFS Error Correction Code (ECC) Algorithm Header
 *
 * This code implements the ECC algorithm used in SmartMedia.
 * 此代码实现了SmartMedia中使用的ECC算法。
 *
 * The ECC comprises 22 bits of parity information and is stuffed into 3 bytes.
 * The two unused bit are set to 1.
 * The ECC can correct single bit errors in a 256-byte page of data.
 * Thus, two such ECC blocks are used on a 512-byte NAND page.
 *
 * ECC包含22位奇偶校验信息，填充到3个字节中。
 * 两个未使用的位设置为1。
 * ECC可以纠正256字节数据页中的单位错误。
 * 因此，在512字节的NAND页上使用两个这样的ECC块。
 */

#ifndef __YAFFS_ECC_H__
#define __YAFFS_ECC_H__

/*
 * ECC其他格式结构 - ECC other format structure
 * 用于存储列奇偶校验和行奇偶校验信息
 * Used to store column parity and line parity information
 */
struct yaffs_ecc_other {
	unsigned char col_parity;		/* 列奇偶校验 - Column parity */
	unsigned line_parity;			/* 行奇偶校验 - Line parity */
	unsigned line_parity_prime;		/* 行奇偶校验素数 - Line parity prime */
};

/* 标准ECC函数 - Standard ECC functions */
void yaffs_ecc_calc(const unsigned char *data, unsigned char *ecc);		/* 计算ECC校验码 - Calculate ECC code */
int yaffs_ecc_correct(unsigned char *data, unsigned char *read_ecc,		/* 使用ECC纠正数据错误 - Correct data errors using ECC */
		      const unsigned char *test_ecc);

/* 其他格式ECC函数 - Other format ECC functions */
void yaffs_ecc_calc_other(const unsigned char *data, unsigned n_bytes,		/* 计算其他格式的ECC - Calculate ECC in other format */
			  struct yaffs_ecc_other *ecc);
int yaffs_ecc_correct_other(unsigned char *data, unsigned n_bytes,		/* 使用其他格式ECC纠正错误 - Correct errors using other format ECC */
			    struct yaffs_ecc_other *read_ecc,
			    const struct yaffs_ecc_other *test_ecc);
#endif
