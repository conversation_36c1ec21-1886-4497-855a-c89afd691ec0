/*
 * YAFFS: Yet Another Flash File System. A NAND-flash specific file system.
 * YAFFS: 又一个闪存文件系统 - 专为NAND闪存设计的文件系统
 *
 * Copyright (C) 2002-2018 Aleph One Ltd.
 *
 * Created by <PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License version 2 as
 * published by the Free Software Foundation.
 */

/*
 * YAFFS对象分配器实现 - YAFFS Object Allocator Implementation
 *
 * 此文件实现了YAFFS的内存分配器，用于管理Tnode和对象的分配
 * This file implements YAFFS memory allocator for managing Tnode and object allocation
 */

#include "yaffs_allocator.h"
#include "yaffs_guts.h"
#include "yaffs_trace.h"
#include "yportenv.h"

/*
 * 简化的slab分配器实现 - Simplified slab allocator implementation
 *
 * Each entry in yaffs_tnode_list and yaffs_obj_list hold blocks
 * of approx 100 objects that are themn allocated singly.
 * This is basically a simplified slab allocator.
 *
 * We don't use the Linux slab allocator because slab does not allow
 * us to dump all the objects in one hit when we do a umount and tear
 * down  all the tnodes and objects. slab requires that we first free
 * the individual objects.
 *
 * Once yaffs has been mainlined I shall try to motivate for a change
 * to slab to provide the extra features we need here.
 *
 * yaffs_tnode_list和yaffs_obj_list中的每个条目保存大约100个对象的块，
 * 然后单独分配。这基本上是一个简化的slab分配器。
 *
 * 我们不使用Linux slab分配器，因为slab不允许我们在卸载时一次性
 * 转储所有对象并拆除所有tnodes和对象。slab要求我们首先释放
 * 各个对象。
 *
 * 一旦yaffs被主线化，我将尝试推动slab的更改，以提供我们这里
 * 需要的额外功能。
 */

/* Tnode链表结构 - Tnode list structure
 * 用于管理Tnode块的链表节点
 * List node for managing Tnode blocks
 */
struct yaffs_tnode_list {
	struct yaffs_tnode_list *next;	/* 指向下一个Tnode链表节点 - Pointer to next Tnode list node */
	struct yaffs_tnode *tnodes;	/* 指向Tnode块 - Pointer to Tnode block */
};

/* 对象链表结构 - Object list structure
 * 用于管理对象块的链表节点
 * List node for managing object blocks
 */
struct yaffs_obj_list {
	struct yaffs_obj_list *next;	/* 指向下一个对象链表节点 - Pointer to next object list node */
	struct yaffs_obj *objects;	/* 指向对象块 - Pointer to object block */
};

/* YAFFS分配器结构 - YAFFS allocator structure
 * 管理Tnode和对象的内存分配
 * Manages memory allocation for Tnodes and objects
 */
struct yaffs_allocator {
	/* Tnode分配相关字段 - Tnode allocation related fields */
	int n_tnodes_created;			/* 已创建的Tnode总数 - Total number of Tnodes created */
	struct yaffs_tnode *free_tnodes;	/* 空闲Tnode链表头 - Head of free Tnode list */
	int n_free_tnodes;			/* 空闲Tnode数量 - Number of free Tnodes */
	struct yaffs_tnode_list *alloc_tnode_list;	/* 已分配Tnode块链表 - List of allocated Tnode blocks */

	/* 对象分配相关字段 - Object allocation related fields */
	int n_obj_created;			/* 已创建的对象总数 - Total number of objects created */
	struct list_head free_objs;		/* 空闲对象链表 - List of free objects */
	int n_free_objects;			/* 空闲对象数量 - Number of free objects */
	struct yaffs_obj_list *allocated_obj_list;	/* 已分配对象块链表 - List of allocated object blocks */
};

/*
 * 清理原始Tnode分配器 - Deinitialize raw Tnode allocator
 * 释放所有已分配的Tnode块并重置分配器状态
 * Free all allocated Tnode blocks and reset allocator state
 */
static void yaffs_deinit_raw_tnodes(struct yaffs_dev *dev)
{
	struct yaffs_allocator *allocator =
	    (struct yaffs_allocator *)dev->allocator;
	struct yaffs_tnode_list *tmp;

	if (!allocator) {
		BUG();	/* 分配器不应为空 - Allocator should not be NULL */
		return;
	}

	/* 遍历并释放所有Tnode块 - Traverse and free all Tnode blocks */
	while (allocator->alloc_tnode_list) {
		tmp = allocator->alloc_tnode_list->next;

		kfree(allocator->alloc_tnode_list->tnodes);	/* 释放Tnode块 - Free Tnode block */
		kfree(allocator->alloc_tnode_list);		/* 释放链表节点 - Free list node */
		allocator->alloc_tnode_list = tmp;
	}

	/* 重置分配器状态 - Reset allocator state */
	allocator->free_tnodes = NULL;
	allocator->n_free_tnodes = 0;
	allocator->n_tnodes_created = 0;
}

/*
 * 初始化原始Tnode分配器 - Initialize raw Tnode allocator
 * 设置Tnode分配器的初始状态
 * Set up initial state of Tnode allocator
 */
static void yaffs_init_raw_tnodes(struct yaffs_dev *dev)
{
	struct yaffs_allocator *allocator = dev->allocator;

	if (!allocator) {
		BUG();	/* 分配器不应为空 - Allocator should not be NULL */
		return;
	}

	/* 初始化Tnode分配器字段 - Initialize Tnode allocator fields */
	allocator->alloc_tnode_list = NULL;
	allocator->free_tnodes = NULL;
	allocator->n_free_tnodes = 0;
	allocator->n_tnodes_created = 0;
}

/*
 * 创建Tnode节点 - Create Tnode nodes
 * 批量分配指定数量的Tnode节点并将它们链接到空闲链表中
 * Batch allocate specified number of Tnode nodes and link them to free list
 */
static int yaffs_create_tnodes(struct yaffs_dev *dev, int n_tnodes)
{
	struct yaffs_allocator *allocator =
	    (struct yaffs_allocator *)dev->allocator;
	int i;
	struct yaffs_tnode *new_tnodes;
	u8 *mem;
	struct yaffs_tnode *curr;
	struct yaffs_tnode *next;
	struct yaffs_tnode_list *tnl;

	if (!allocator) {
		BUG();	/* 分配器不应为空 - Allocator should not be NULL */
		return YAFFS_FAIL;
	}

	if (n_tnodes < 1)
		return YAFFS_OK;	/* 无需分配 - No need to allocate */

	/* 分配内存块 - Allocate memory block */
	new_tnodes = kmalloc(n_tnodes * dev->tnode_size, GFP_NOFS);
	mem = (u8 *) new_tnodes;

	if (!new_tnodes) {
		yaffs_trace(YAFFS_TRACE_ERROR,
			"yaffs: Could not allocate Tnodes");
		return YAFFS_FAIL;
	}

	/* 为宽Tnode建立新的链接 - New hookup for wide tnodes
	 * 将所有新分配的Tnode链接成一个链表
	 * Link all newly allocated Tnodes into a linked list
	 */
	for (i = 0; i < n_tnodes - 1; i++) {
		curr = (struct yaffs_tnode *)&mem[i * dev->tnode_size];
		next = (struct yaffs_tnode *)&mem[(i + 1) * dev->tnode_size];
		curr->internal[0] = next;	/* 使用第一个内部指针作为链表指针 - Use first internal pointer as list pointer */
	}

	/* 将最后一个节点链接到现有的空闲链表 - Link last node to existing free list */
	curr = (struct yaffs_tnode *)&mem[(n_tnodes - 1) * dev->tnode_size];
	curr->internal[0] = allocator->free_tnodes;
	allocator->free_tnodes = (struct yaffs_tnode *)mem;

	/* 更新统计信息 - Update statistics */
	allocator->n_free_tnodes += n_tnodes;
	allocator->n_tnodes_created += n_tnodes;

	/* 将这批Tnode添加到管理链表中以便稍后释放 -
	 * Now add this bunch of tnodes to a list for freeing up.
	 * NB If we can't add this to the management list it isn't fatal
	 * but it just means we can't free this bunch of tnodes later.
	 *
	 * 注意：如果无法添加到管理链表中并不致命，
	 * 只是意味着稍后无法释放这批Tnode
	 */
	tnl = kmalloc(sizeof(struct yaffs_tnode_list), GFP_NOFS);
	if (!tnl) {
		yaffs_trace(YAFFS_TRACE_ERROR,
			"Could not add tnodes to management list");
		return YAFFS_FAIL;
	} else {
		tnl->tnodes = new_tnodes;
		tnl->next = allocator->alloc_tnode_list;
		allocator->alloc_tnode_list = tnl;
	}

	yaffs_trace(YAFFS_TRACE_ALLOCATE, "Tnodes added");

	return YAFFS_OK;
}

/*
 * 分配原始Tnode - Allocate raw Tnode
 * 从空闲链表中获取一个Tnode，如果没有则创建新的
 * Get a Tnode from free list, create new ones if none available
 */
struct yaffs_tnode *yaffs_alloc_raw_tnode(struct yaffs_dev *dev)
{
	struct yaffs_allocator *allocator =
	    (struct yaffs_allocator *)dev->allocator;
	struct yaffs_tnode *tn = NULL;

	if (!allocator) {
		BUG();	/* 分配器不应为空 - Allocator should not be NULL */
		return NULL;
	}

	/* 如果没有剩余的Tnode，创建更多 - If there are none left make more */
	if (!allocator->free_tnodes)
		yaffs_create_tnodes(dev, YAFFS_ALLOCATION_NTNODES);

	if (allocator->free_tnodes) {
		tn = allocator->free_tnodes;	/* 获取链表头 - Get head of list */
		allocator->free_tnodes = allocator->free_tnodes->internal[0];	/* 更新链表头 - Update list head */
		allocator->n_free_tnodes--;	/* 减少空闲计数 - Decrease free count */
	}

	return tn;
}

/*
 * 释放原始Tnode - Free raw Tnode
 * FreeTnode frees up a tnode and puts it back on the free list
 * 释放一个Tnode并将其放回空闲链表
 */
void yaffs_free_raw_tnode(struct yaffs_dev *dev, struct yaffs_tnode *tn)
{
	struct yaffs_allocator *allocator = dev->allocator;

	if (!allocator) {
		BUG();	/* 分配器不应为空 - Allocator should not be NULL */
		return;
	}

	if (tn) {
		tn->internal[0] = allocator->free_tnodes;	/* 链接到空闲链表头 - Link to head of free list */
		allocator->free_tnodes = tn;			/* 更新链表头 - Update list head */
		allocator->n_free_tnodes++;			/* 增加空闲计数 - Increase free count */
	}
	dev->checkpoint_blocks_required = 0;	/* 强制重新计算 - Force recalculation */
}

/*--------------- yaffs_obj对象分配 - yaffs_obj allocation ------------------------
 *
 * Free yaffs_objs are stored in a list using obj->siblings.
 * The blocks of allocated objects are stored in a linked list.
 *
 * 空闲的yaffs_obj使用obj->siblings存储在链表中。
 * 已分配对象的块存储在链表中。
 */

/*
 * 初始化原始对象分配器 - Initialize raw object allocator
 * 设置对象分配器的初始状态
 * Set up initial state of object allocator
 */
static void yaffs_init_raw_objs(struct yaffs_dev *dev)
{
	struct yaffs_allocator *allocator = dev->allocator;

	if (!allocator) {
		BUG();	/* 分配器不应为空 - Allocator should not be NULL */
		return;
	}

	allocator->allocated_obj_list = NULL;		/* 清空已分配对象链表 - Clear allocated object list */
	INIT_LIST_HEAD(&allocator->free_objs);		/* 初始化空闲对象链表 - Initialize free object list */
	allocator->n_free_objects = 0;			/* 重置空闲对象计数 - Reset free object count */
}

static void yaffs_deinit_raw_objs(struct yaffs_dev *dev)
{
	struct yaffs_allocator *allocator = dev->allocator;
	struct yaffs_obj_list *tmp;

	if (!allocator) {
		BUG();
		return;
	}

	while (allocator->allocated_obj_list) {
		tmp = allocator->allocated_obj_list->next;
		kfree(allocator->allocated_obj_list->objects);
		kfree(allocator->allocated_obj_list);
		allocator->allocated_obj_list = tmp;
	}

	INIT_LIST_HEAD(&allocator->free_objs);
	allocator->n_free_objects = 0;
	allocator->n_obj_created = 0;
}

static int yaffs_create_free_objs(struct yaffs_dev *dev, int n_obj)
{
	struct yaffs_allocator *allocator = dev->allocator;
	int i;
	struct yaffs_obj *new_objs;
	struct yaffs_obj_list *list;

	if (!allocator) {
		BUG();
		return YAFFS_FAIL;
	}

	if (n_obj < 1)
		return YAFFS_OK;

	/* make these things */
	new_objs = kmalloc(n_obj * sizeof(struct yaffs_obj), GFP_NOFS);
	list = kmalloc(sizeof(struct yaffs_obj_list), GFP_NOFS);

	if (!new_objs || !list) {
		kfree(new_objs);
		new_objs = NULL;
		kfree(list);
		list = NULL;
		yaffs_trace(YAFFS_TRACE_ALLOCATE,
			"Could not allocate more objects");
		return YAFFS_FAIL;
	}

	/* Hook them into the free list */
	for (i = 0; i < n_obj; i++)
		list_add(&new_objs[i].siblings, &allocator->free_objs);

	allocator->n_free_objects += n_obj;
	allocator->n_obj_created += n_obj;

	/* Now add this bunch of Objects to a list for freeing up. */

	list->objects = new_objs;
	list->next = allocator->allocated_obj_list;
	allocator->allocated_obj_list = list;

	return YAFFS_OK;
}

struct yaffs_obj *yaffs_alloc_raw_obj(struct yaffs_dev *dev)
{
	struct yaffs_obj *obj = NULL;
	struct list_head *lh;
	struct yaffs_allocator *allocator = dev->allocator;

	if (!allocator) {
		BUG();
		return obj;
	}

	/* If there are none left make more */
	if (list_empty(&allocator->free_objs))
		yaffs_create_free_objs(dev, YAFFS_ALLOCATION_NOBJECTS);

	if (!list_empty(&allocator->free_objs)) {
		lh = allocator->free_objs.next;
		obj = list_entry(lh, struct yaffs_obj, siblings);
		list_del_init(lh);
		allocator->n_free_objects--;
	}

	return obj;
}

void yaffs_free_raw_obj(struct yaffs_dev *dev, struct yaffs_obj *obj)
{

	struct yaffs_allocator *allocator = dev->allocator;

	if (!allocator) {
		BUG();
		return;
	}

	/* Link into the free list. */
	list_add(&obj->siblings, &allocator->free_objs);
	allocator->n_free_objects++;
}

void yaffs_deinit_raw_tnodes_and_objs(struct yaffs_dev *dev)
{

	if (!dev->allocator) {
		BUG();
		return;
	}

	yaffs_deinit_raw_tnodes(dev);
	yaffs_deinit_raw_objs(dev);
	kfree(dev->allocator);
	dev->allocator = NULL;
}

void yaffs_init_raw_tnodes_and_objs(struct yaffs_dev *dev)
{
	struct yaffs_allocator *allocator;

	if (dev->allocator) {
		BUG();
		return;
	}

	allocator = kmalloc(sizeof(struct yaffs_allocator), GFP_NOFS);
	if (allocator) {
		dev->allocator = allocator;
		yaffs_init_raw_tnodes(dev);
		yaffs_init_raw_objs(dev);
	}
}

