/*
 * YAFFS: Yet another Flash File System . A NAND-flash specific file system.
 * YAFFS: 又一个闪存文件系统 - 专为NAND闪存设计的文件系统
 *
 * Copyright (C) 2002-2018 Aleph One Ltd.
 *
 * Created by <PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU Lesser General Public License version 2.1 as
 * published by the Free Software Foundation.
 *
 * Note: Only YAFFS headers are LGPL, YAFFS C code is covered by GPL.
 */

/*
 * YAFFS对象分配器头文件 - YAFFS Object Allocator Header
 *
 * 此模块负责管理YAFFS中Tnode和对象的内存分配
 * This module manages memory allocation for Tnodes and objects in YAFFS
 */

#ifndef __YAFFS_ALLOCATOR_H__
#define __YAFFS_ALLOCATOR_H__

#include "yaffs_guts.h"

/* Tnode和对象分配器初始化/清理函数 - Tnode and object allocator init/cleanup functions */
void yaffs_init_raw_tnodes_and_objs(struct yaffs_dev *dev);		/* 初始化原始Tnode和对象分配器 - Initialize raw Tnode and object allocators */
void yaffs_deinit_raw_tnodes_and_objs(struct yaffs_dev *dev);		/* 清理原始Tnode和对象分配器 - Deinitialize raw Tnode and object allocators */

/* Tnode分配和释放函数 - Tnode allocation and deallocation functions */
struct yaffs_tnode *yaffs_alloc_raw_tnode(struct yaffs_dev *dev);	/* 分配原始Tnode - Allocate raw Tnode */
void yaffs_free_raw_tnode(struct yaffs_dev *dev, struct yaffs_tnode *tn);	/* 释放原始Tnode - Free raw Tnode */

/* 对象分配和释放函数 - Object allocation and deallocation functions */
struct yaffs_obj *yaffs_alloc_raw_obj(struct yaffs_dev *dev);		/* 分配原始对象 - Allocate raw object */
void yaffs_free_raw_obj(struct yaffs_dev *dev, struct yaffs_obj *obj);	/* 释放原始对象 - Free raw object */

#endif
