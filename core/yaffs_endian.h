/*
 * YAFFS: Yet another Flash File System . A NAND-flash specific file system.
 * YAFFS: 又一个闪存文件系统 - 专为NAND闪存设计的文件系统
 *
 * Copyright (C) 2002-2018 Aleph One Ltd.
 *
 * Created by <PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU Lesser General Public License version 2.1 as
 * published by the Free Software Foundation.
 *
 * Note: Only YAFFS headers are LGPL, YAFFS C code is covered by GPL.
 */

/*
 * YAFFS字节序处理头文件 - YAFFS Endianness Handling Header
 *
 * 此模块提供大小端字节序转换功能，确保跨平台兼容性
 * This module provides endianness conversion functionality to ensure cross-platform compatibility
 */

#ifndef __YAFFS_ENDIAN_H__
#define __YAFFS_ENDIAN_H__
#include "yaffs_guts.h"
#include "yaffs_packedtags2.h"

/*
 * 32位无符号整数字节序交换 - 32-bit unsigned integer endian swap
 * 将大端序转换为小端序，或反之
 * Convert big-endian to little-endian, or vice versa
 */
static inline u32 swap_u32(u32 val)
{
	return ((val >>24) & 0x000000ff) |	/* 最高字节移到最低位 - Move highest byte to lowest */
	       ((val >> 8) & 0x0000ff00) |	/* 次高字节移到次低位 - Move second highest to second lowest */
	       ((val << 8) & 0x00ff0000) |	/* 次低字节移到次高位 - Move second lowest to second highest */
	       ((val <<24) & 0xff000000);	/* 最低字节移到最高位 - Move lowest byte to highest */
}

/*
 * 64位无符号整数字节序交换 - 64-bit unsigned integer endian swap
 * 处理64位数据的字节序转换
 * Handle endianness conversion for 64-bit data
 */
static inline u64 swap_u64(u64 val)
{
	return ((val >> 56) & 0x00000000000000ff) |	/* 字节7->0 - Byte 7->0 */
	       ((val >> 40) & 0x000000000000ff00) |	/* 字节6->1 - Byte 6->1 */
	       ((val >> 24) & 0x0000000000ff0000) |	/* 字节5->2 - Byte 5->2 */
	       ((val >> 8)  & 0x00000000ff000000) |	/* 字节4->3 - Byte 4->3 */
	       ((val << 8)  & 0x000000ff00000000) |	/* 字节3->4 - Byte 3->4 */
	       ((val << 24) & 0x0000ff0000000000) |	/* 字节2->5 - Byte 2->5 */
	       ((val << 40) & 0x00ff000000000000) |	/* 字节1->6 - Byte 1->6 */
	       ((val << 56) & 0xff00000000000000);	/* 字节0->7 - Byte 0->7 */
}

/*
 * 时间类型字节序交换 - Time type endian swap
 * 根据时间类型的大小选择合适的交换函数
 * Choose appropriate swap function based on time type size
 */
static inline YTIME_T swap_ytime_t(YTIME_T val)
{
	if (sizeof(YTIME_T) == sizeof(u64))
		return swap_u64(val);		/* 64位时间类型 - 64-bit time type */
	else
		return swap_u32(val);		/* 32位时间类型 - 32-bit time type */
}

/* 有符号32位整数字节序交换宏 - Signed 32-bit integer endian swap macro */
#define swap_s32(val) \
	(s32)(swap_u32((u32)(val)))		/* 交换有符号32位整数 - Swap signed 32-bit integer */

/*
 * 文件偏移量字节序交换 - File offset endian swap
 * 处理文件偏移量的字节序转换，支持32位和64位
 * Handle endianness conversion for file offsets, supporting both 32-bit and 64-bit
 */
static inline loff_t swap_loff_t(loff_t lval)
{
	u32 vall = swap_u32(FSIZE_LOW(lval));	/* 交换低32位 - Swap low 32 bits */
	u32 valh;

	if (sizeof(loff_t) == sizeof(u32))
		return (loff_t) vall;		/* 32位系统直接返回 - Return directly for 32-bit systems */

	valh = swap_u32(FSIZE_HIGH(lval));	/* 交换高32位 - Swap high 32 bits */

	return FSIZE_COMBINE(vall, valh);	/* 注意：高低位已交换 - NB: h and l are swapped */
}



struct yaffs_dev;

/* 字节序处理函数 - Endianness handling functions */
void yaffs_do_endian_s32(struct yaffs_dev *dev, s32 *val);			/* 处理有符号32位整数字节序 - Handle signed 32-bit integer endianness */
void yaffs_do_endian_u32(struct yaffs_dev *dev, u32 *val);			/* 处理无符号32位整数字节序 - Handle unsigned 32-bit integer endianness */
void yaffs_do_endian_oh(struct yaffs_dev *dev, struct yaffs_obj_hdr *oh);	/* 处理对象头字节序 - Handle object header endianness */
void yaffs_do_endian_packed_tags2(struct yaffs_dev *dev,			/* 处理打包标签2字节序 - Handle packed tags2 endianness */
				struct yaffs_packed_tags2_tags_only *ptt);
void yaffs_endian_config(struct yaffs_dev *dev);				/* 配置设备字节序 - Configure device endianness */

#endif
