/*
 * YAFFS: Yet another Flash File System . A NAND-flash specific file system.
 * YAFFS: 又一个闪存文件系统 - 专为NAND闪存设计的文件系统
 *
 * Copyright (C) 2002-2018 Aleph One Ltd.
 *
 * Created by <PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU Lesser General Public License version 2.1 as
 * published by the Free Software Foundation.
 *
 * Note: Only YAFFS headers are LGPL, YAFFS C code is covered by GPL.
 */

/*
 * YAFFS标签兼容性头文件 - YAFFS Tags Compatibility Header
 *
 * 此模块提供YAFFS1和YAFFS2标签格式之间的兼容性支持
 * This module provides compatibility support between YAFFS1 and YAFFS2 tag formats
 */

#ifndef __YAFFS_TAGSCOMPAT_H__
#define __YAFFS_TAGSCOMPAT_H__

#include "yaffs_guts.h"

#if 0
/*
 * 以下函数当前被禁用 - The following functions are currently disabled
 * 这些是标签兼容性的读写和块管理函数
 * These are tag compatibility read/write and block management functions
 */

int yaffs_tags_compat_wr(struct yaffs_dev *dev,				/* 兼容性标签写入 - Compatibility tag write */
			 int nand_chunk,
			 const u8 *data, const struct yaffs_ext_tags *tags);
int yaffs_tags_compat_rd(struct yaffs_dev *dev,				/* 兼容性标签读取 - Compatibility tag read */
			 int nand_chunk,
			 u8 *data, struct yaffs_ext_tags *tags);
int yaffs_tags_compat_mark_bad(struct yaffs_dev *dev, int block_no);	/* 兼容性坏块标记 - Compatibility bad block marking */
int yaffs_tags_compat_query_block(struct yaffs_dev *dev,		/* 兼容性块查询 - Compatibility block query */
				  int block_no,
				  enum yaffs_block_state *state,
				  u32 *seq_number);
#endif

/* 标签兼容性和ECC函数 - Tag compatibility and ECC functions */
void yaffs_tags_compat_install(struct yaffs_dev *dev);			/* 安装标签兼容性支持 - Install tag compatibility support */
void yaffs_calc_tags_ecc(struct yaffs_tags *tags);			/* 计算标签ECC - Calculate tags ECC */
int yaffs_check_tags_ecc(struct yaffs_tags *tags);			/* 检查标签ECC - Check tags ECC */

#endif
