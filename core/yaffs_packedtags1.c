/*
 * YAFFS: Yet Another Flash File System. A NAND-flash specific file system.
 * YAFFS: 又一个闪存文件系统 - 专为NAND闪存设计的文件系统
 *
 * Copyright (C) 2002-2018 Aleph One Ltd.
 *
 * Created by <PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License version 2 as
 * published by the Free Software Foundation.
 */

/*
 * YAFFS1打包标签实现 - YAFFS1 Packed Tags Implementation
 *
 * 此文件实现了YAFFS1标签格式的打包和解包功能
 * This file implements packing and unpacking functionality for YAFFS1 tag format
 */

#include "yaffs_packedtags1.h"
#include "yportenv.h"

/*
 * 全FF模式数组 - All FF pattern array
 * 用于检测未使用的标签区域（擦除状态）
 * Used to detect unused tag areas (erased state)
 */
static const u8 all_ff[20] = {
	0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff
};

/*
 * 打包YAFFS1标签 - Pack YAFFS1 tags
 * 将扩展标签格式转换为YAFFS1打包格式
 * Convert extended tag format to YAFFS1 packed format
 */
void yaffs_pack_tags1(struct yaffs_packed_tags1 *pt,
		      const struct yaffs_ext_tags *t)
{
	pt->chunk_id = t->chunk_id;			/* 数据块ID - Chunk ID */
	pt->serial_number = t->serial_number;		/* 序列号 - Serial number */
	pt->n_bytes = t->n_bytes;			/* 字节数 - Number of bytes */
	pt->obj_id = t->obj_id;				/* 对象ID - Object ID */
	pt->ecc = 0;					/* ECC设为0 - ECC set to 0 */
	pt->deleted = (t->is_deleted) ? 0 : 1;		/* 删除标志（反转） - Delete flag (inverted) */
	pt->unused_stuff = 0;				/* 未使用字段 - Unused field */
	pt->should_be_ff = 0xffffffff;			/* 应为0xffffffff的字段 - Field that should be 0xffffffff */
}

/*
 * 解包YAFFS1标签 - Unpack YAFFS1 tags
 * 将YAFFS1打包格式转换为扩展标签格式
 * Convert YAFFS1 packed format to extended tag format
 */
void yaffs_unpack_tags1(struct yaffs_ext_tags *t,
			const struct yaffs_packed_tags1 *pt)
{
	/* 检查是否为有效标签（不是全FF） - Check if valid tags (not all FF) */
	if (memcmp(all_ff, pt, sizeof(struct yaffs_packed_tags1))) {
		t->block_bad = 0;			/* 默认块不坏 - Block not bad by default */
		if (pt->should_be_ff != 0xffffffff)
			t->block_bad = 1;		/* 如果标记字段不正确，标记为坏块 - Mark as bad block if marker field incorrect */
		t->chunk_used = 1;			/* 标记数据块已使用 - Mark chunk as used */
		t->obj_id = pt->obj_id;			/* 对象ID - Object ID */
		t->chunk_id = pt->chunk_id;		/* 数据块ID - Chunk ID */
		t->n_bytes = pt->n_bytes;		/* 字节数 - Number of bytes */
		t->ecc_result = YAFFS_ECC_RESULT_NO_ERROR;	/* ECC结果 - ECC result */
		t->is_deleted = (pt->deleted) ? 0 : 1;	/* 删除标志（反转回来） - Delete flag (invert back) */
		t->serial_number = pt->serial_number;	/* 序列号 - Serial number */
	} else {
		/* 全FF表示未使用的标签 - All FF means unused tags */
		memset(t, 0, sizeof(struct yaffs_ext_tags));
	}
}
