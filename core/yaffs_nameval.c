/*
 * YAFFS: Yet Another Flash File System. A NAND-flash specific file system.
 * YAFFS: 又一个闪存文件系统 - 专为NAND闪存设计的文件系统
 *
 * Copyright (C) 2002-2018 Aleph One Ltd.
 *
 * Created by <PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License version 2 as
 * published by the Free Software Foundation.
 */

/*
 * YAFFS名值对处理实现 - YAFFS Name-Value Pair Handling Implementation
 *
 * This simple implementation of a name-value store assumes a small number of
 * values and fits into a small finite buffer.
 * 这个简单的名值存储实现假设有少量的值，并适合小的有限缓冲区。
 *
 * Each attribute is stored as a record:
 * 每个属性存储为一个记录：
 *  sizeof(size) bytes   record size.
 *  sizeof(size)字节     记录大小
 *  strnlen+1 bytes name null terminated.
 *  strnlen+1字节        以null结尾的名称
 *  nbytes    value.
 *  nbytes字节           值
 *  ----------
 *  total size  stored in record size
 *  总大小存储在记录大小中
 *
 * This code has not been tested with unicode yet.
 * 此代码尚未使用unicode进行测试。
 */

#include "yaffs_nameval.h"
#include "yaffs_guts.h"
#include "yportenv.h"
#include "yaffs_endian.h"

/*
 * 查找名值对 - Find name-value pair
 * 在扩展属性缓冲区中查找指定名称的属性
 * Find attribute with specified name in extended attribute buffer
 */
static int nval_find(struct yaffs_dev *dev,
		     const char *xb, int xb_size, const YCHAR *name,
		     int *exist_size)
{
	int pos = 0;	/* 当前位置 - Current position */
	s32 size;	/* 记录大小 - Record size */

	memcpy(&size, xb, sizeof(size));	/* 读取第一个记录大小 - Read first record size */
	yaffs_do_endian_s32(dev, &size);	/* 处理字节序 - Handle endianness */

	/* 遍历所有记录 - Traverse all records */
	while (size > 0 && (size < xb_size) && (pos + size < xb_size)) {
		/* 比较名称 - Compare names */
		if (!strncmp((YCHAR *) (xb + pos + sizeof(size)),
				name, size)) {
			if (exist_size)
				*exist_size = size;	/* 返回存在的大小 - Return existing size */
			return pos;			/* 返回找到的位置 - Return found position */
		}
		pos += size;	/* 移动到下一个记录 - Move to next record */
		if (pos < (int)(xb_size - sizeof(size))) {
			memcpy(&size, xb + pos, sizeof(size));	/* 读取下一个记录大小 - Read next record size */
			yaffs_do_endian_s32(dev, &size);	/* 处理字节序 - Handle endianness */
		} else
			size = 0;	/* 到达缓冲区末尾 - Reached end of buffer */
	}
	if (exist_size)
		*exist_size = 0;	/* 未找到 - Not found */
	return -ENODATA;		/* 返回未找到错误 - Return not found error */
}

/*
 * 计算已使用空间 - Calculate used space
 * 计算扩展属性缓冲区中已使用的字节数
 * Calculate number of bytes used in extended attribute buffer
 */
static int nval_used(struct yaffs_dev *dev, const char *xb, int xb_size)
{
	int pos = 0;	/* 当前位置 - Current position */
	s32 size;	/* 记录大小 - Record size */

	memcpy(&size, xb + pos, sizeof(size));	/* 读取记录大小 - Read record size */
	yaffs_do_endian_s32(dev, &size);	/* 处理字节序 - Handle endianness */

	/* 遍历所有记录计算总大小 - Traverse all records to calculate total size */
	while (size > 0 && (size < xb_size) && (pos + size < xb_size)) {
		pos += size;	/* 累加位置 - Accumulate position */
		if (pos < (int)(xb_size - sizeof(size))) {
			memcpy(&size, xb + pos, sizeof(size));	/* 读取下一个记录大小 - Read next record size */
			yaffs_do_endian_s32(dev, &size);	/* 处理字节序 - Handle endianness */
		} else
			size = 0;	/* 到达缓冲区末尾 - Reached end of buffer */
	}
	return pos;	/* 返回已使用的字节数 - Return number of bytes used */
}

int nval_del(struct yaffs_dev *dev, char *xb, int xb_size, const YCHAR *name)
{
	int pos = nval_find(dev, xb, xb_size, name, NULL);
	s32 size;

	if (pos < 0 || pos >= xb_size)
		return -ENODATA;

	/* Find size, shift rest over this record,
	 * then zero out the rest of buffer */
	memcpy(&size, xb + pos, sizeof(size));
	yaffs_do_endian_s32(dev, &size);

	memcpy(xb + pos, xb + pos + size, xb_size - (pos + size));
	memset(xb + (xb_size - size), 0, size);
	return 0;
}

int nval_set(struct yaffs_dev *dev,
	     char *xb, int xb_size, const YCHAR *name, const char *buf,
	     int bsize, int flags)
{
	int pos;
	int namelen = strnlen(name, xb_size);
	int size_exist = 0;
	int space;
	int start;
	s32 reclen;
	s32 reclen_endianised;

	pos = nval_find(dev, xb, xb_size, name, &size_exist);

	if (flags & XATTR_CREATE && pos >= 0)
		return -EEXIST;
	if (flags & XATTR_REPLACE && pos < 0)
		return -ENODATA;

	start = nval_used(dev, xb, xb_size);
	space = xb_size - start + size_exist;

	reclen = (sizeof(reclen) + namelen + 1 + bsize);

	if (reclen > space)
		return -ENOSPC;

	if (pos >= 0) {
		/* Exists, so delete it. */
		nval_del(dev, xb, xb_size, name);
		start = nval_used(dev, xb, xb_size);
	}

	pos = start;

	reclen_endianised = reclen;
	yaffs_do_endian_s32(dev, &reclen_endianised);
	memcpy(xb + pos, &reclen_endianised, sizeof(reclen_endianised));
	pos += sizeof(reclen_endianised);
	strncpy((YCHAR *) (xb + pos), name, reclen);
	pos += (namelen + 1);
	memcpy(xb + pos, buf, bsize);
	return 0;
}

int nval_get(struct yaffs_dev *dev,
	     const char *xb, int xb_size, const YCHAR * name, char *buf,
	     int bsize)
{
	int pos = nval_find(dev, xb, xb_size, name, NULL);
	s32 size;

	if (pos >= 0 && pos < xb_size) {

		memcpy(&size, xb + pos, sizeof(size));
		yaffs_do_endian_s32(dev, &size);
		pos += sizeof(size);	/* advance past record length */
		size -= sizeof(size);

		/* Advance over name string */
		while (xb[pos] && size > 0 && pos < xb_size) {
			pos++;
			size--;
		}
		/*Advance over NUL */
		pos++;
		size--;

		/* If bsize is zero then this is a size query.
		 * Return the size, but don't copy.
		 */
		if (!bsize)
			return size;

		if (size <= bsize) {
			memcpy(buf, xb + pos, size);
			return size;
		}
	}
	if (pos >= 0)
		return -ERANGE;

	return -ENODATA;
}

int nval_list(struct yaffs_dev *dev, const char *xb, int xb_size, char *buf, int bsize)
{
	int pos = 0;
	s32 size;
	int name_len;
	int ncopied = 0;
	int filled = 0;

	memcpy(&size, xb + pos, sizeof(size));
	yaffs_do_endian_s32(dev, &size);

	while (size > (int)(sizeof(size)) &&
		size <= xb_size &&
		(pos + size) < xb_size &&
		!filled) {
		pos += sizeof(size);
		size -= sizeof(size);
		name_len = strnlen((YCHAR *) (xb + pos), size);
		if (ncopied + name_len + 1 < bsize) {
			memcpy(buf, xb + pos, name_len * sizeof(YCHAR));
			buf += name_len;
			*buf = '\0';
			buf++;
			if (sizeof(YCHAR) > 1) {
				*buf = '\0';
				buf++;
			}
			ncopied += (name_len + 1);
		} else {
			filled = 1;
		}
		pos += size;
		if (pos < (int)(xb_size - sizeof(size))) {
			memcpy(&size, xb + pos, sizeof(size));
			yaffs_do_endian_s32(dev, &size);
		}
		else
			size = 0;
	}
	return ncopied;
}

int nval_hasvalues(struct yaffs_dev *dev, const char *xb, int xb_size)
{
	return nval_used(dev, xb, xb_size) > 0;
}
