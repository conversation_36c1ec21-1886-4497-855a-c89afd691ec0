/*
 * YAFFS: Yet another Flash File System . A NAND-flash specific file system.
 * YAFFS: 又一个闪存文件系统 - 专为NAND闪存设计的文件系统
 *
 * Copyright (C) 2002-2018 Aleph One Ltd.
 *
 * Created by <PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU Lesser General Public License version 2.1 as
 * published by the Free Software Foundation.
 *
 * Note: Only YAFFS headers are LGPL, YAFFS C code is covered by GPL.
 */

#ifndef __YAFFS_GUTS_H__
#define __YAFFS_GUTS_H__

#include "yportenv.h"

/* 函数返回值定义 - Function return values */
#define YAFFS_OK	1		/* 操作成功 - Operation successful */
#define YAFFS_FAIL  0		/* 操作失败 - Operation failed */

/* YAFFS魔数定义 - YAFFS magic number definition
 * Give us a  Y=0x59,
 * Give us an A=0x41,
 * Give us an FF=0xff
 * Give us an S=0x53
 * And what have we got...
 * 组合成YAFFS的魔数，用于识别YAFFS文件系统
 */
#define YAFFS_MAGIC			0x5941ff53

/*
 * Tnode树结构定义 - Tnode tree structure definition
 * Tnodes form a tree with the tnodes in "levels"
 * Levels greater than 0 hold 8 slots which point to other tnodes.
 * Those at level 0 hold 16 slots which point to chunks in NAND.
 *
 * Tnode形成一个分层的树结构：
 * - 大于0级的节点包含8个槽位，指向其他Tnode
 * - 0级节点包含16个槽位，直接指向NAND中的数据块
 *
 * A maximum level of 8 thust supports files of size up to:
 * 最大8级支持的文件大小计算公式：
 * 2^(3*MAX_LEVEL+4)
 *
 * Thus a max level of 8 supports files with up to 2^^28 chunks which gives
 * a maximum file size of around 512Gbytees with 2k chunks.
 * 因此最大8级支持2^28个数据块，在2K块大小下最大文件约512GB
 */
#define YAFFS_NTNODES_LEVEL0		16	/* 0级节点槽位数 - Level 0 node slots */
#define YAFFS_TNODES_LEVEL0_BITS	4	/* 0级节点位数 - Level 0 node bits */
#define YAFFS_TNODES_LEVEL0_MASK	0xf	/* 0级节点掩码 - Level 0 node mask */

#define YAFFS_NTNODES_INTERNAL		(YAFFS_NTNODES_LEVEL0 / 2)	/* 内部节点槽位数 - Internal node slots */
#define YAFFS_TNODES_INTERNAL_BITS	(YAFFS_TNODES_LEVEL0_BITS - 1)	/* 内部节点位数 - Internal node bits */
#define YAFFS_TNODES_INTERNAL_MASK	0x7				/* 内部节点掩码 - Internal node mask */
#define YAFFS_TNODES_MAX_LEVEL		8				/* 最大树深度 - Maximum tree depth */
#define YAFFS_TNODES_MAX_BITS		(YAFFS_TNODES_LEVEL0_BITS + \
					YAFFS_TNODES_INTERNAL_BITS * \
					YAFFS_TNODES_MAX_LEVEL)		/* 最大位数 - Maximum bits */
#define YAFFS_MAX_CHUNK_ID		((1 << YAFFS_TNODES_MAX_BITS) - 1)	/* 最大块ID - Maximum chunk ID */

#define YAFFS_MAX_FILE_SIZE_32		0x7fffffff			/* 32位系统最大文件大小 - Max file size on 32-bit systems */

/* YAFFS1模式常量定义 - Constants for YAFFS1 mode */
#define YAFFS_BYTES_PER_SPARE		16	/* 每个备用区字节数 - Bytes per spare area */
#define YAFFS_BYTES_PER_CHUNK		512	/* 每个数据块字节数 - Bytes per chunk */
#define YAFFS_CHUNK_SIZE_SHIFT		9	/* 数据块大小位移 - Chunk size shift (2^9=512) */
#define YAFFS_CHUNKS_PER_BLOCK		32	/* 每个擦除块的数据块数 - Chunks per erase block */
#define YAFFS_BYTES_PER_BLOCK	(YAFFS_CHUNKS_PER_BLOCK*YAFFS_BYTES_PER_CHUNK)	/* 每个擦除块字节数 - Bytes per erase block */

/* YAFFS2模式最小值定义 - YAFFS2 mode minimum values */
#define YAFFS_MIN_YAFFS2_CHUNK_SIZE	1024	/* YAFFS2最小数据块大小 - Minimum YAFFS2 chunk size */
#define YAFFS_MIN_YAFFS2_SPARE_SIZE	32	/* YAFFS2最小备用区大小 - Minimum YAFFS2 spare size */

/* 内存分配相关常量 - Memory allocation related constants */
#define YAFFS_ALLOCATION_NOBJECTS	100	/* 对象分配批次大小 - Object allocation batch size */
#define YAFFS_ALLOCATION_NTNODES	100	/* Tnode分配批次大小 - Tnode allocation batch size */
#define YAFFS_ALLOCATION_NLINKS		100	/* 链接分配批次大小 - Link allocation batch size */

#define YAFFS_NOBJECT_BUCKETS		256	/* 对象哈希桶数量 - Number of object hash buckets */

/* 对象ID空间定义 - Object ID space definition */
#define YAFFS_OBJECT_SPACE		0x40000			/* 对象ID空间大小 - Object ID space size */
#define YAFFS_MAX_OBJECT_ID		(YAFFS_OBJECT_SPACE - 1)	/* 最大对象ID - Maximum object ID */

/* 二进制数据版本标记 - Binary data version stamps */
#define YAFFS_SUMMARY_VERSION		1	/* 摘要数据版本 - Summary data version */

/* 文件名长度限制 - Filename length limits */
#ifdef CONFIG_YAFFS_UNICODE
#define YAFFS_MAX_NAME_LENGTH		127	/* Unicode模式最大文件名长度 - Max filename length in Unicode mode */
#define YAFFS_MAX_ALIAS_LENGTH		79	/* Unicode模式最大别名长度 - Max alias length in Unicode mode */
#else
#define YAFFS_MAX_NAME_LENGTH		255	/* ASCII模式最大文件名长度 - Max filename length in ASCII mode */
#define YAFFS_MAX_ALIAS_LENGTH		159	/* ASCII模式最大别名长度 - Max alias length in ASCII mode */
#endif

#define YAFFS_SHORT_NAME_LENGTH		15	/* 短文件名长度 - Short filename length */

/* 特殊伪对象的对象ID定义 - Special object IDs for pseudo objects */
#define YAFFS_OBJECTID_ROOT		1	/* 根目录对象ID - Root directory object ID */
#define YAFFS_OBJECTID_LOSTNFOUND	2	/* lost+found目录对象ID - lost+found directory object ID */
#define YAFFS_OBJECTID_UNLINKED		3	/* 未链接对象ID - Unlinked objects ID */
#define YAFFS_OBJECTID_DELETED		4	/* 已删除对象ID - Deleted objects ID */

/* 摘要数据的虚拟对象ID - Fake object ID for summary data */
#define YAFFS_OBJECTID_SUMMARY		0x10	/* 摘要数据对象ID - Summary data object ID */

/* 检查点相关的伪对象ID - Pseudo object IDs for checkpointing */
#define YAFFS_OBJECTID_CHECKPOINT_DATA	0x20	/* 检查点数据对象ID - Checkpoint data object ID */
#define YAFFS_SEQUENCE_CHECKPOINT_DATA	0x21	/* 检查点序列数据ID - Checkpoint sequence data ID */

/* 缓存相关定义 - Cache related definitions */
#define YAFFS_MAX_SHORT_OP_CACHES	20	/* 最大短操作缓存数 - Maximum short operation caches */

#define YAFFS_N_TEMP_BUFFERS		6	/* 临时缓冲区数量 - Number of temporary buffers */

/* 数据块写入重试限制 - Chunk write retry limits
 * We limit the number attempts at sucessfully saving a chunk of data.
 * Small-page devices have 32 pages per block; large-page devices have 64.
 * Default to something in the order of 5 to 10 blocks worth of chunks.
 *
 * 我们限制成功保存数据块的尝试次数：
 * - 小页设备每块32页，大页设备每块64页
 * - 默认设置为大约5-10个块的页数
 */
#define YAFFS_WR_ATTEMPTS		(5*64)	/* 写入尝试次数 - Write attempt count */

/* YAFFS2序列号定义 - YAFFS2 sequence number definitions
 * Sequence numbers are used in YAFFS2 to determine block allocation order.
 * The range is limited slightly to help distinguish bad numbers from good.
 * This also allows us to perhaps in the future use special numbers for
 * special purposes.
 * EFFFFF00 allows the allocation of 8 blocks/second (~1Mbytes) for 15 years,
 * and is a larger number than the lifetime of a 2GB device.
 *
 * 序列号用于YAFFS2中确定块分配顺序：
 * - 范围稍有限制以区分好坏序列号
 * - 允许未来使用特殊序列号
 * - EFFFFF00允许15年内以8块/秒(约1MB/秒)的速度分配
 */
#define YAFFS_LOWEST_SEQUENCE_NUMBER	0x00001000	/* 最低序列号 - Lowest sequence number */
#define YAFFS_HIGHEST_SEQUENCE_NUMBER	0xefffff00	/* 最高序列号 - Highest sequence number */

/* 坏块特殊序列号 - Special sequence number for bad blocks */
#define YAFFS_SEQUENCE_BAD_BLOCK	0xffff0000	/* 标记失败的坏块序列号 - Sequence number for bad block that failed to be marked */

/* 数据块缓存结构 - Chunk cache structure
 * Chunk cache is used for short read/write operations.
 * 数据块缓存用于短读写操作
 */
struct yaffs_cache {
	struct yaffs_obj *object;	/* 缓存所属的对象 - Object that owns this cache */
	int chunk_id;			/* 数据块ID - Chunk ID */
	int last_use;			/* 最后使用时间戳 - Last use timestamp */
	int dirty;			/* 脏标志，表示需要写回 - Dirty flag, indicates need to write back */
	int n_bytes;			/* 有效字节数，仅在脏时有效 - Valid bytes, only valid if cache is dirty */
	int locked;			/* 锁定标志，锁定时不能推出或刷新 - Lock flag, can't push out or flush while locked */
	u8 *data;			/* 缓存数据指针 - Pointer to cached data */
};

/* 缓存管理器结构 - Cache manager structure */
struct yaffs_cache_manager {
	struct yaffs_cache *cache;	/* 缓存数组指针 - Pointer to cache array */
	int n_caches;			/* 缓存数量 - Number of caches */
	int cache_last_use;		/* 缓存最后使用计数器 - Cache last use counter */
	int n_temp_buffers;		/* 临时缓冲区数量 - Number of temporary buffers */
};

/* YAFFS1标签结构（内存中） - YAFFS1 tags structures in RAM
 * NB This uses bitfield. Bitfields should not straddle a u32 boundary
 * otherwise the structure size will get blown out.
 *
 * 注意：使用位域结构，位域不应跨越u32边界，否则结构大小会膨胀
 */
struct yaffs_tags {
	u32 chunk_id:20;		/* 数据块ID (20位) - Chunk ID (20 bits) */
	u32 serial_number:2;		/* 序列号 (2位) - Serial number (2 bits) */
	u32 n_bytes_lsb:10;		/* 字节数低10位 - Lower 10 bits of byte count */
	u32 obj_id:18;			/* 对象ID (18位) - Object ID (18 bits) */
	u32 ecc:12;			/* 错误校正码 (12位) - Error correction code (12 bits) */
	u32 n_bytes_msb:2;		/* 字节数高2位 - Upper 2 bits of byte count */
};

/* YAFFS1标签联合体 - YAFFS1 tags union
 * 提供不同的访问方式：结构体、字节数组、32位整数数组
 * Provides different access methods: struct, byte array, u32 array
 */
union yaffs_tags_union {
	struct yaffs_tags as_tags;	/* 作为标签结构访问 - Access as tags structure */
	u8  as_bytes[8];		/* 作为字节数组访问 - Access as byte array */
	u32 as_u32[2];			/* 作为32位整数数组访问 - Access as u32 array */
};


/* YAFFS2扩展标签相关定义 - Stuff used for extended tags in YAFFS2 */

/* ECC错误校正结果枚举 - ECC error correction result enumeration */
enum yaffs_ecc_result {
	YAFFS_ECC_RESULT_UNKNOWN,	/* 未知结果 - Unknown result */
	YAFFS_ECC_RESULT_NO_ERROR,	/* 无错误 - No error */
	YAFFS_ECC_RESULT_FIXED,		/* 错误已修复 - Error fixed */
	YAFFS_ECC_RESULT_UNFIXED	/* 错误无法修复 - Error unfixed */
};

/*
 * 对象类型枚举 - Object type enumeration
 * Object type enum:
 * When this is stored in flash we store it as a u32 instead
 * to prevent any alignment change issues as compiler variants change.
 *
 * 当存储到闪存时，我们将其存储为u32类型，
 * 以防止编译器变化导致的对齐问题
 */
enum yaffs_obj_type {
	YAFFS_OBJECT_TYPE_UNKNOWN,	/* 未知类型 - Unknown type */
	YAFFS_OBJECT_TYPE_FILE,		/* 普通文件 - Regular file */
	YAFFS_OBJECT_TYPE_SYMLINK,	/* 符号链接 - Symbolic link */
	YAFFS_OBJECT_TYPE_DIRECTORY,	/* 目录 - Directory */
	YAFFS_OBJECT_TYPE_HARDLINK,	/* 硬链接 - Hard link */
	YAFFS_OBJECT_TYPE_SPECIAL	/* 特殊文件(设备文件等) - Special file (device files, etc.) */
};

#define YAFFS_OBJECT_TYPE_MAX YAFFS_OBJECT_TYPE_SPECIAL	/* 最大对象类型值 - Maximum object type value */

/* YAFFS扩展标签结构 - YAFFS extended tags structure
 * 用于YAFFS2的增强标签信息，包含更多元数据
 * Used for enhanced tag information in YAFFS2, contains more metadata
 */
struct yaffs_ext_tags {
	unsigned chunk_used;		/* 数据块使用状态：已使用或未使用 - Status of the chunk: used or unused */
	unsigned obj_id;		/* 对象ID，为0表示未使用 - Object ID, 0 means not used */
	unsigned chunk_id;		/* 数据块ID，为0表示对象头，否则为数据块 - Chunk ID, 0 means header, else data chunk */
	unsigned n_bytes;		/* 有效字节数，仅对数据块有效 - Valid bytes, only valid for data chunks */

	/* 以下字段仅在读取时有意义 - The following stuff only has meaning when we read */
	enum yaffs_ecc_result ecc_result;	/* ECC校正结果 - ECC correction result */
	unsigned block_bad;			/* 坏块标志 - Bad block flag */

	/* YAFFS1相关字段 - YAFFS1 stuff */
	unsigned is_deleted;		/* 数据块删除标记 - The chunk is marked deleted */
	unsigned serial_number;		/* YAFFS1的2位序列号 - YAFFS1 2-bit serial number */

	/* YAFFS2相关字段 - YAFFS2 stuff */
	unsigned seq_number;		/* 此块的序列号 - The sequence number of this block */

	/* 对象头额外信息（仅YAFFS2） - Extra info if this is an object header (YAFFS2 only) */
	unsigned extra_available;	/* 额外信息可用标志，非零表示可用 - Extra info available if not zero */
	unsigned extra_parent_id;	/* 父对象ID - The parent object ID */
	unsigned extra_is_shrink;	/* 是否为收缩头 - Is it a shrink header? */
	unsigned extra_shadows;		/* 是否影射其他对象 - Does this shadow another object? */

	enum yaffs_obj_type extra_obj_type;	/* 对象类型 - What object type? */

	loff_t extra_file_size;		/* 文件长度（如果是文件） - Length if it is a file */
	unsigned extra_equiv_id;	/* 硬链接的等价对象ID - Equivalent object ID for a hard link */
};

/* YAFFS1备用区结构 - Spare structure for YAFFS1
 * 定义YAFFS1中NAND闪存备用区的布局
 * Defines the layout of NAND flash spare area in YAFFS1
 */
struct yaffs_spare {
	u8 tb0;			/* 标签字节0 - Tag byte 0 */
	u8 tb1;			/* 标签字节1 - Tag byte 1 */
	u8 tb2;			/* 标签字节2 - Tag byte 2 */
	u8 tb3;			/* 标签字节3 - Tag byte 3 */
	u8 page_status;		/* 页状态，设为0删除数据块 - Page status, set to 0 to delete the chunk */
	u8 block_status;	/* 块状态 - Block status */
	u8 tb4;			/* 标签字节4 - Tag byte 4 */
	u8 tb5;			/* 标签字节5 - Tag byte 5 */
	u8 ecc1[3];		/* 第一个ECC校验码 - First ECC code */
	u8 tb6;			/* 标签字节6 - Tag byte 6 */
	u8 tb7;			/* 标签字节7 - Tag byte 7 */
	u8 ecc2[3];		/* 第二个ECC校验码 - Second ECC code */
};

/* 传递给MTD的特殊结构 - Special structure for passing through to MTD
 * 包含备用区数据和ECC结果
 * Contains spare area data and ECC results
 */
struct yaffs_nand_spare {
	struct yaffs_spare spare;	/* 备用区数据 - Spare area data */
	int eccres1;			/* 第一个ECC结果 - First ECC result */
	int eccres2;			/* 第二个ECC结果 - Second ECC result */
};

/* 内存中的块数据 - Block data in RAM */

/* 块状态枚举 - Block state enumeration
 * 定义YAFFS中擦除块的各种状态
 * Defines various states of erase blocks in YAFFS
 */
enum yaffs_block_state {
	YAFFS_BLOCK_STATE_UNKNOWN = 0,	/* 未知状态 - Unknown state */

	YAFFS_BLOCK_STATE_SCANNING,	/* 正在扫描 - Being scanned */

	YAFFS_BLOCK_STATE_NEEDS_SCAN,	/* 需要扫描状态 - Needs scanning state */
	/* The block might have something on it (ie it is allocating or full,
	 * perhaps empty) but it needs to be scanned to determine its true
	 * state.
	 * This state is only valid during scanning.
	 * NB We tolerate empty because the pre-scanner might be incapable of
	 * deciding
	 * However, if this state is returned on a YAFFS2 device,
	 * then we expect a sequence number
	 *
	 * 块可能包含数据（正在分配、已满或空），但需要扫描确定真实状态。
	 * 此状态仅在扫描期间有效。
	 * 注意：我们容忍空块，因为预扫描器可能无法决定。
	 * 但是，如果在YAFFS2设备上返回此状态，我们期望有序列号。
	 */

	YAFFS_BLOCK_STATE_EMPTY,	/* 空块状态 - Empty block state */
	/* This block is empty - 此块为空 */

	YAFFS_BLOCK_STATE_ALLOCATING,	/* 分配中状态 - Allocating state */
	/* This block is partially allocated.
	 * At least one page holds valid data.
	 * This is the one currently being used for page
	 * allocation. Should never be more than one of these.
	 * If a block is only partially allocated at mount it is treated as
	 * full.
	 *
	 * 此块部分分配：
	 * - 至少一页包含有效数据
	 * - 这是当前用于页分配的块
	 * - 不应该有超过一个这样的块
	 * - 如果挂载时块只是部分分配，则视为已满
	 */

	YAFFS_BLOCK_STATE_FULL,		/* 已满状态 - Full state */
	/* All the pages in this block have been allocated.
	 * If a block was only partially allocated when mounted we treat
	 * it as fully allocated.
	 *
	 * 此块中的所有页都已分配。
	 * 如果挂载时块只是部分分配，我们将其视为完全分配。
	 */

	YAFFS_BLOCK_STATE_DIRTY,	/* 脏块状态 - Dirty state */
	/* The block was full and now all chunks have been deleted.
	 * Erase me, reuse me.
	 *
	 * 块曾经是满的，现在所有数据块都已删除。
	 * 擦除我，重用我。
	 */

	YAFFS_BLOCK_STATE_CHECKPOINT,	/* 检查点状态 - Checkpoint state */
	/* This block is assigned to holding checkpoint data.
	 * 此块被分配用于保存检查点数据。
	 */

	YAFFS_BLOCK_STATE_COLLECTING,	/* 垃圾回收状态 - Garbage collecting state */
	/* This block is being garbage collected
	 * 此块正在进行垃圾回收
	 */

	YAFFS_BLOCK_STATE_DEAD		/* 死块状态 - Dead state */
	/* This block has failed and is not in use
	 * 此块已失效且不再使用
	 */
};

#define	YAFFS_NUMBER_OF_BLOCK_STATES (YAFFS_BLOCK_STATE_DEAD + 1)	/* 块状态总数 - Total number of block states */

/* 块信息结构 - Block information structure
 * 存储每个擦除块的状态和统计信息
 * Stores status and statistics for each erase block
 */
struct yaffs_block_info {
	s32 soft_del_pages:10;		/* 软删除页数 - Number of soft deleted pages */
	s32 pages_in_use:10;		/* 使用中的页数 - Number of pages in use */
	u32 block_state:4;		/* 块状态，上述状态之一 - One of the above block states */
					/* NB use unsigned because enum is sometimes
					 * an int
					 * 注意：使用无符号类型，因为枚举有时是int类型
					 */
	u32 needs_retiring:1;		/* 此块数据失效，需要退役 - Data has failed on this block, need to retire */
	u32 skip_erased_check:1;	/* 跳过此块的擦除检查 - Skip the erased check on this block */
	u32 gc_prioritise:1;		/* ECC检查或空白检查失败，块应优先进行垃圾回收 -
					 * An ECC check or blank check has failed.
					 * Block should be prioritised for GC
					 */
	u32 chunk_error_strikes:3;	/* 此块发生ECC等错误并尝试重用的次数 -
					 * How many times we've had ECC etc failures
					 * on this block and tried to reuse it
					 */
	u32 has_summary:1;		/* 此块包含摘要信息 - The block has a summary */
	u32 has_shrink_hdr:1;		/* 此块至少有一个收缩头 - This block has at least one shrink header */
	u32 seq_number;			/* YAFFS2的块序列号 - Block sequence number for YAFFS2 */
};

/* 块信息联合体 - Block information union
 * 提供结构体和32位整数数组两种访问方式
 * Provides both struct and u32 array access methods
 */
union yaffs_block_info_union {
	struct yaffs_block_info bi;	/* 作为块信息结构访问 - Access as block info structure */
	u32	as_u32[2];		/* 作为32位整数数组访问 - Access as u32 array */
};

/* -------------------------- 对象结构 - Object structure -------------------------------*/
/* 存储在NAND中的对象结构 - This is the object structure as stored on NAND */

/* 对象头结构 - Object header structure
 * 定义存储在NAND闪存中的对象头格式
 * Defines the object header format stored in NAND flash
 */
struct yaffs_obj_hdr {
	u32 type;			/* 对象类型 - Object type (enum yaffs_obj_type) */

	/* 适用于所有对象 - Apply to everything */
	u32 parent_obj_id;		/* 父对象ID - Parent object ID */
	u16 sum_no_longer_used;		/* 文件名校验和，不再使用 - Checksum of name, no longer used */
	YCHAR name[YAFFS_MAX_NAME_LENGTH + 1];	/* 对象名称 - Object name */

	/* 以下字段适用于除硬链接外的所有对象类型 - The following apply to all object types except for hard links */
	u32 yst_mode;			/* 文件权限和类型 - File permissions and type */

	u32 yst_uid;			/* 用户ID - User ID */
	u32 yst_gid;			/* 组ID - Group ID */
	u32 yst_atime;			/* 访问时间 - Access time */
	u32 yst_mtime;			/* 修改时间 - Modification time */
	u32 yst_ctime;			/* 创建时间 - Creation time */

	/* 文件大小仅适用于文件 - File size applies to files only */
	u32 file_size_low;		/* 文件大小低32位 - Low 32 bits of file size */

	/* 等价对象ID仅适用于硬链接 - Equivalent object ID applies to hard links only */
	int equiv_id;			/* 硬链接的等价对象ID - Equivalent object ID for hard links */

	/* 别名仅用于符号链接 - Alias is for symlinks only */
	YCHAR alias[YAFFS_MAX_ALIAS_LENGTH + 1];	/* 符号链接别名 - Symbolic link alias */

	u32 yst_rdev;			/* 块设备和字符设备信息(主/次设备号) - Block and char device info (major/minor) */

	/*
	 * WinCE时间字段不再仅用于存储WinCE时间
	 * WinCE times are no longer just used to store WinCE times.
	 * They are also used to store 64-bit times.
	 * We actually store and read the times in both places and use
	 * the best we can.
	 *
	 * 它们也用于存储64位时间。
	 * 我们实际上在两个地方存储和读取时间，并使用最佳的那个。
	 */
	u32 win_ctime[2];		/* WinCE创建时间/64位时间 - WinCE creation time / 64-bit time */
	u32 win_atime[2];		/* WinCE访问时间/64位时间 - WinCE access time / 64-bit time */
	u32 win_mtime[2];		/* WinCE修改时间/64位时间 - WinCE modification time / 64-bit time */

	u32 inband_shadowed_obj_id;	/* 带内影射对象ID - Inband shadowed object ID */
	u32 inband_is_shrink;		/* 带内收缩标志 - Inband shrink flag */

	u32 file_size_high;		/* 文件大小高32位 - High 32 bits of file size */
	u32 reserved[1];		/* 保留字段 - Reserved field */
	int shadows_obj;		/* 此对象头影射的对象，>0时有效 -
					 * This object header shadows the specified object if > 0
					 */

	/* 收缩标志适用于创建空洞时写入的对象头 - is_shrink applies to object headers written when we make a hole */
	u32 is_shrink;			/* 收缩标志 - Shrink flag */
};

/*--------------------------- Tnode树节点 - Tnode -------------------------- */

/* Tnode结构 - Tnode structure
 * 用于构建文件数据块索引树的节点
 * Node used to build the file data chunk index tree
 */
struct yaffs_tnode {
	struct yaffs_tnode *internal[YAFFS_NTNODES_INTERNAL];	/* 内部节点指针数组 - Internal node pointer array */
};

/*------------------------  对象 - Object -----------------------------*/
/* 对象可以是以下类型之一 - An object can be one of:
 * - a directory (no data, has children links) - 目录（无数据，有子链接）
 * - a regular file (data.... not prunes :->). - 普通文件（有数据）
 * - a symlink [symbolic link] (the alias). - 符号链接（别名）
 * - a hard link - 硬链接
 */

/* 文件变体有三种文件大小 - The file variant has three file sizes:
 *  - file_size : size of file as written into Yaffs - including data in cache.
 *    文件大小：写入YAFFS的文件大小 - 包括缓存中的数据
 *  - stored_size - size of file as stored on media.
 *    存储大小：存储在介质上的文件大小
 *  - shrink_size - size of file that has been shrunk back to.
 *    收缩大小：文件被收缩回的大小
 *
 * The stored_size and file_size might be different because the data written
 * into the cache will increase the file_size but the stored_size will only
 * change when the data is actually stored.
 *
 * stored_size和file_size可能不同，因为写入缓存的数据会增加file_size，
 * 但stored_size只有在数据实际存储时才会改变。
 */
struct yaffs_file_var {
	loff_t file_size;		/* 文件大小 - File size */
	loff_t stored_size;		/* 存储大小 - Stored size */
	loff_t shrink_size;		/* 收缩大小 - Shrink size */
	int top_level;			/* 顶层级别 - Top level */
	struct yaffs_tnode *top;	/* 顶层Tnode指针 - Top level Tnode pointer */
};

/* 目录变体结构 - Directory variant structure */
struct yaffs_dir_var {
	struct list_head children;	/* 子对象链表 - List of child links */
	struct list_head dirty;		/* 脏目录链表条目 - Entry for list of dirty directories */
};

/* 符号链接变体结构 - Symbolic link variant structure */
struct yaffs_symlink_var {
	YCHAR *alias;			/* 符号链接别名 - Symbolic link alias */
};

/* 硬链接变体结构 - Hard link variant structure */
struct yaffs_hardlink_var {
	struct yaffs_obj *equiv_obj;	/* 等价对象指针 - Equivalent object pointer */
	u32 equiv_id;			/* 等价对象ID - Equivalent object ID */
};

/* 对象变体联合体 - Object variant union
 * 根据对象类型存储不同的数据结构
 * Stores different data structures based on object type
 */
union yaffs_obj_var {
	struct yaffs_file_var file_variant;		/* 文件变体 - File variant */
	struct yaffs_dir_var dir_variant;		/* 目录变体 - Directory variant */
	struct yaffs_symlink_var symlink_variant;	/* 符号链接变体 - Symbolic link variant */
	struct yaffs_hardlink_var hardlink_variant;	/* 硬链接变体 - Hard link variant */
};

/* YAFFS对象结构 - YAFFS object structure
 * 这是YAFFS文件系统中所有对象（文件、目录、链接等）的核心数据结构
 * This is the core data structure for all objects (files, directories, links, etc.) in YAFFS
 */
struct yaffs_obj {
	u8 deleted:1;		/* 已删除标志，仅适用于未链接文件 - Deleted flag, should only apply to unlinked files */
	u8 soft_del:1;		/* 软删除标志 - Soft deleted flag */
	u8 unlinked:1;		/* 未链接文件标志 - Unlinked file flag */
	u8 fake:1;		/* 虚假对象标志，在NAND中无实体 - Fake object flag, has no presence on NAND */
	u8 rename_allowed:1;	/* 允许重命名标志，某些对象不能重命名 - Rename allowed flag, some objects cannot be renamed */
	u8 unlink_allowed:1;	/* 允许取消链接标志 - Unlink allowed flag */
	u8 dirty:1;		/* 脏标志，对象需要写入闪存 - Dirty flag, the object needs to be written to flash */
	u8 valid:1;		/* 有效标志，文件系统加载时此对象可能在数据可用前创建
				 * Valid flag - When the file system is being loaded up, this
				 * object might be created before the data is available
				 * ie. file data chunks encountered before the header.
				 */
	u8 lazy_loaded:1;	/* 延迟加载标志，此对象已延迟加载但缺少某些细节
				 * Lazy loaded flag - This object has been lazy loaded and
				 * is missing some detail
				 */

	u8 defered_free:1;	/* 延迟释放标志，对象已从NAND移除但仍在inode缓存中
				 * Deferred free flag - Object is removed from NAND, but is
				 * still in the inode cache. Free of object is deferred
				 * until the inode is released.
				 */
	u8 being_created:1;	/* 正在创建标志，此对象仍在创建中，跳过某些验证检查
				 * Being created flag - This object is still being created
				 * so skip some verification checks.
				 */
	u8 is_shadowed:1;	/* 影射标志，此对象在重命名过程中被影射
				 * Shadowed flag - This object is shadowed on the way
				 * to being renamed.
				 */

	u8 xattr_known:1;	/* 扩展属性已知标志，我们知道此对象是否有扩展属性
				 * Extended attributes known flag - We know if this object has xattribs or not
				 */
	u8 has_xattr:1;		/* 有扩展属性标志，此对象有扩展属性（仅在xattr_known为真时有效）
				 * Has extended attributes flag - This object has xattribs.
				 * Only valid if xattr_known.
				 */

	u8 serial;		/* NAND中数据块的序列号 - Serial number of chunk in NAND */
	u16 sum;		/* 名称校验和，用于加速搜索 - Sum of the name to speed searching */

	struct yaffs_dev *my_dev;	/* 所属设备指针 - The device I'm on */

	struct list_head hash_link;	/* 哈希桶中的对象链表 - List of objects in hash bucket */

	struct list_head hard_links;	/* 硬链接对象链 - Hard linked object chain */

	/* 目录结构相关 - Directory structure stuff */
	/* 也用于链接空闲列表 - Also used for linking up the free list */
	struct yaffs_obj *parent;	/* 父对象指针 - Parent object pointer */
	struct list_head siblings;	/* 兄弟对象链表 - Sibling objects list */

	/* 对象头在NAND中的位置 - Where's my object header in NAND? */
	int hdr_chunk;			/* 对象头数据块号 - Object header chunk number */

	int n_data_chunks;		/* 此文件的数据块数量 - Number of data chunks for this file */

	u32 obj_id;			/* 对象ID值 - The object ID value */

	u32 yst_mode;			/* 文件模式和权限 - File mode and permissions */

	YCHAR short_name[YAFFS_SHORT_NAME_LENGTH + 1];	/* 短文件名 - Short filename */

#ifdef CONFIG_YAFFS_WINCE
	/* WinCE环境下时间字段总是64位 - These are always 64 bits in WinCE */
	u32 win_ctime[2];		/* WinCE创建时间 - WinCE creation time */
	u32 win_mtime[2];		/* WinCE修改时间 - WinCE modification time */
	u32 win_atime[2];		/* WinCE访问时间 - WinCE access time */
#else
	/* 非WinCE环境下时间字段可以是32位或64位 - These can be 32 or 64 bits in non-WinCE */
	YTIME_T yst_uid;		/* 用户ID - User ID */
	YTIME_T yst_gid;		/* 组ID - Group ID */
	YTIME_T yst_atime;		/* 访问时间 - Access time */
	YTIME_T yst_mtime;		/* 修改时间 - Modification time */
	YTIME_T yst_ctime;		/* 创建时间 - Creation time */
#endif

	u32 yst_rdev;			/* 设备号（用于设备文件） - Device number (for device files) */

	void *my_inode;			/* 指向操作系统特定inode的指针 - Pointer to OS-specific inode */

	u32 variant_type;		/* 对象类型变体（enum yaffs_object_type） - Object type variant (enum yaffs_object_type) */

	union yaffs_obj_var variant;	/* 对象变体数据 - Object variant data */

};

/* 对象哈希桶结构 - Object hash bucket structure
 * 用于管理哈希表中的对象链表
 * Used to manage object lists in hash table
 */
struct yaffs_obj_bucket {
	struct list_head list;		/* 对象链表头 - Object list head */
	int count;			/* 桶中对象数量 - Number of objects in bucket */
};


/*--------------------- 临时缓冲区 - Temporary buffers ----------------
 * 这些是数据块大小的工作缓冲区，每个设备有几个
 * These are chunk-sized working buffers. Each device has a few.
 */

/* 临时缓冲区结构 - Temporary buffer structure */
struct yaffs_buffer {
	u8 *buffer;			/* 缓冲区指针 - Buffer pointer */
	int in_use;			/* 使用中标志 - In use flag */
};

/*----------------- 设备 - Device ---------------------------------*/

/* YAFFS参数结构 - YAFFS parameter structure
 * 包含YAFFS设备的配置参数
 * Contains configuration parameters for YAFFS device
 */
struct yaffs_param {
	const YCHAR *name;		/* 设备名称 - Device name */

	/*
	 * 早期设置的入口参数，YAFFS设置其余部分
	 * Entry parameters set up way early. Yaffs sets up the rest.
	 * 使用前应将结构清零，以便未使用和默认值为零
	 * The structure should be zeroed out before use so that unused
	 * and default values are zero.
	 */

	int inband_tags;		/* 使用带内标签 - Use inband tags */
	u32 total_bytes_per_chunk;	/* 每个数据块的总字节数，应>=512，不需要是2的幂
					 * Total bytes per chunk - Should be >= 512, does not need to be a power of 2
					 */
	u32 chunks_per_block;		/* 每个擦除块的数据块数，不需要是2的幂 - Chunks per block, does not need to be a power of 2 */
	u32 spare_bytes_per_chunk;	/* 每个数据块的备用区大小 - Spare area size per chunk */
	u32 start_block;		/* 允许使用的起始块 - Start block we're allowed to use */
	u32 end_block;			/* 允许使用的结束块 - End block we're allowed to use */
	u32 n_reserved_blocks;		/* 保留块数量，可调整以减少NOR和RAM上的保留块
					 * Number of reserved blocks - Tuneable so that we can reduce
					 * reserved blocks on NOR and RAM.
					 */

	u32 n_caches;			/* 缓存数量，如果==0则禁用短操作缓存，否则为短操作缓存数量
					 * Number of caches - If == 0, then short op caching is disabled,
					 * else the number of short op caches.
					 */
	int cache_bypass_aligned;	/* 对齐写入绕过缓存标志，非零则对齐写入绕过缓存
					 * Cache bypass for aligned writes - If non-zero then bypass the cache for aligned writes
					 */

	int use_nand_ecc;		/* 是否使用NAND驱动ECC标志，决定是否在数据上使用NAND驱动ECC（yaffs1）
					 * Use NAND ECC flag - Flag to decide whether or not to use NAND driver ECC on data (yaffs1)
					 */
	int tags_9bytes;		/* 使用9字节标签 - Use 9 byte tags */
	int no_tags_ecc;		/* 标签ECC禁用标志，决定是否对打包标签进行ECC（yaffs2）
					 * No tags ECC flag - Flag to decide whether or not to do ECC on packed tags (yaffs2)
					 */

	int is_yaffs2;			/* 在此设备上使用yaffs2模式 - Use yaffs2 mode on this device */

	int empty_lost_n_found;		/* 挂载时自动清空lost+found目录 - Auto-empty lost+found directory on mount */

	int refresh_period;		/* 检查块刷新的频率 - How often to check for a block refresh */

	/* 检查点控制，可在初始化前后设置 - Checkpoint control. Can be set before or after initialisation */
	u8 skip_checkpt_rd;		/* 跳过检查点读取 - Skip checkpoint read */
	u8 skip_checkpt_wr;		/* 跳过检查点写入 - Skip checkpoint write */

	int enable_xattr;		/* 启用扩展属性 - Enable extended attributes */

	int max_objects;		/* 限制创建的对象数量，0=无限制
					 * Set to limit the number of objects created.
					 * 0 = no limit.
					 */

	int hide_lost_n_found;		/* 设置非零值隐藏lost-n-found目录 - Set non-zero to hide the lost-n-found dir. */

	int stored_endian;		/* 存储字节序：0=CPU字节序，1=小端，2=大端 - 0=cpu endian, 1=little endian, 2=big endian */

	/* remove_obj_fn函数必须由需要它的操作系统提供
	 * The remove_obj_fn function must be supplied by OS flavours that need it.
	 * yaffs direct使用它实现更快的readdir
	 * yaffs direct uses it to implement the faster readdir.
	 * Linux使用它在解锁期间保护目录
	 * Linux uses it to protect the directory during unlocking.
	 */
	void (*remove_obj_fn) (struct yaffs_obj *obj);	/* 移除对象回调函数 - Remove object callback function */

	/* 标记超级块为脏的回调函数 - Callback to mark the superblock dirty */
	void (*sb_dirty_fn) (struct yaffs_dev *dev);

	/* 控制垃圾回收的回调函数 - Callback to control garbage collection */
	unsigned (*gc_control_fn) (struct yaffs_dev *dev);

	/* 调试控制标志，除非你知道自己在做什么，否则不要使用 - Debug control flags. Don't use unless you know what you're doing */
	int use_header_file_size;	/* 是否使用头部文件大小标志，决定是否使用头部的文件大小
					 * Flag to determine if we should use file sizes from the header
					 */
	int disable_lazy_load;		/* 在此设备上禁用延迟加载 - Disable lazy loading on this device */
	int wide_tnodes_disabled;	/* 设置为禁用宽Tnode - Set to disable wide tnodes */
	int disable_soft_del;		/* 仅yaffs1：设置为禁用软删除
					 * yaffs 1 only: Set to disable the use of softdeletion.
					 */

	int defered_dir_update;		/* 设置为延迟目录更新 - Set to defer directory updates */

#ifdef CONFIG_YAFFS_AUTO_UNICODE
	int auto_unicode;		/* 自动Unicode支持 - Automatic Unicode support */
#endif
	int always_check_erased;	/* 强制始终检查数据块擦除状态 - Force chunk erased check always on */

	int disable_summary;		/* 禁用摘要功能 - Disable summary feature */
	int disable_bad_block_marking;	/* 禁用坏块标记 - Disable bad block marking */

};

/* YAFFS驱动程序结构 - YAFFS driver structure
 * 定义底层NAND闪存操作的函数指针
 * Defines function pointers for low-level NAND flash operations
 */
struct yaffs_driver {
	int (*drv_write_chunk_fn) (struct yaffs_dev *dev, int nand_chunk,
				   const u8 *data, int data_len,
				   const u8 *oob, int oob_len);		/* 写数据块函数 - Write chunk function */
	int (*drv_read_chunk_fn) (struct yaffs_dev *dev, int nand_chunk,
				   u8 *data, int data_len,
				   u8 *oob, int oob_len,
				   enum yaffs_ecc_result *ecc_result);	/* 读数据块函数 - Read chunk function */
	int (*drv_erase_fn) (struct yaffs_dev *dev, int block_no);	/* 擦除块函数 - Erase block function */
	int (*drv_mark_bad_fn) (struct yaffs_dev *dev, int block_no);	/* 标记坏块函数 - Mark bad block function */
	int (*drv_check_bad_fn) (struct yaffs_dev *dev, int block_no);	/* 检查坏块函数 - Check bad block function */
	int (*drv_initialise_fn) (struct yaffs_dev *dev);		/* 驱动初始化函数 - Driver initialise function */
	int (*drv_deinitialise_fn) (struct yaffs_dev *dev);		/* 驱动去初始化函数 - Driver deinitialise function */
};

/* YAFFS标签处理器结构 - YAFFS tags handler structure
 * 定义标签读写和块查询操作的函数指针
 * Defines function pointers for tag read/write and block query operations
 */
struct yaffs_tags_handler {
	int (*write_chunk_tags_fn) (struct yaffs_dev *dev,
				    int nand_chunk, const u8 *data,
				    const struct yaffs_ext_tags *tags);	/* 写数据块和标签函数 - Write chunk with tags function */
	int (*read_chunk_tags_fn) (struct yaffs_dev *dev,
				   int nand_chunk, u8 *data,
				   struct yaffs_ext_tags *tags);	/* 读数据块和标签函数 - Read chunk with tags function */

	int (*query_block_fn) (struct yaffs_dev *dev, int block_no,
			       enum yaffs_block_state *state,
			       u32 *seq_number);			/* 查询块状态函数 - Query block state function */
	int (*mark_bad_fn) (struct yaffs_dev *dev, int block_no);	/* 标记坏块函数 - Mark bad block function */
};

/* YAFFS设备结构 - YAFFS device structure
 * 这是YAFFS文件系统的核心设备结构，包含所有设备相关的状态和配置
 * This is the core device structure for YAFFS filesystem, containing all device-related state and configuration
 */
struct yaffs_dev {
	struct yaffs_param param;		/* 设备参数 - Device parameters */
	struct yaffs_driver drv;		/* 驱动程序接口 - Driver interface */
	struct yaffs_tags_handler tagger;	/* 标签处理器 - Tags handler */

	/* 上下文存储，保存此设备的额外操作系统特定数据 - Context storage. Holds extra OS specific data for this device */

	void *os_context;			/* 操作系统上下文 - OS context */
	void *driver_context;			/* 驱动程序上下文 - Driver context */

	struct list_head dev_list;		/* 设备链表 - Device list */

	int ll_init;				/* 底层初始化标志 - Low level initialization flag */
	/* 运行时参数，由YAFFS设置 - Runtime parameters. Set up by YAFFS. */
	u32 data_bytes_per_chunk;		/* 每个数据块的数据字节数 - Data bytes per chunk */

	/* 非宽Tnode相关 - Non-wide tnode stuff */
	u16 chunk_grp_bits;			/* 如果Tnode不够宽，需要解析的位数
					 * Number of bits that need to be resolved if
					 * the tnodes are not wide enough.
					 */
	u16 chunk_grp_size;			/* == 2^chunk_grp_bits */

	struct yaffs_tnode *tn_swap_buffer;	/* Tnode交换缓冲区 - Tnode swap buffer */

	/* 支持宽Tnode的相关数据 - Stuff to support wide tnodes */
	u32 tnode_width;			/* Tnode宽度 - Tnode width */
	u32 tnode_mask;				/* Tnode掩码 - Tnode mask */
	u32 tnode_size;				/* Tnode大小 - Tnode size */

	/* 用于计算文件偏移到数据块转换的相关数据 - Stuff for figuring out file offset to chunk conversions */
	u32 chunk_shift;			/* 位移值 - Shift value */
	u32 chunk_div;				/* 位移后的除数：对于2^n大小为1 - Divisor after shifting: 1 for 2^n sizes */
	u32 chunk_mask;				/* 用于2的幂情况的掩码 - Mask to use for power-of-2 case */

	int is_mounted;				/* 已挂载标志 - Mounted flag */
	int read_only;				/* 只读标志 - Read only flag */
	int is_checkpointed;			/* 已检查点标志 - Checkpointed flag */
	int swap_endian;			/* 存储的字节序需要字节序交换 - Stored endian needs endian swap */

	/* Stuff to support block offsetting to support start block zero */
	u32 internal_start_block;
	u32 internal_end_block;
	int block_offset;
	int chunk_offset;

	/* Runtime checkpointing stuff */
	int checkpt_page_seq;	/* running sequence number of checkpt pages */
	int checkpt_byte_count;
	int checkpt_byte_offs;
	u8 *checkpt_buffer;
	int checkpt_open_write;
	u32 blocks_in_checkpt;
	int checkpt_cur_chunk;
	int checkpt_cur_block;
	int checkpt_next_block;
	int *checkpt_block_list;
	u32 checkpt_max_blocks;
	u32 checkpt_sum;
	u32 checkpt_xor;

	int checkpoint_blocks_required;	/* Number of blocks needed to store
					 * current checkpoint set */

	/* Block Info */
	struct yaffs_block_info *block_info;
	u8 *chunk_bits;		/* bitmap of chunks in use */
	u8 block_info_alt:1;	/* allocated using alternative alloc */
	u8 chunk_bits_alt:1;	/* allocated using alternative alloc */
	int chunk_bit_stride;	/* Number of bytes of chunk_bits per block.
				 * Must be consistent with chunks_per_block.
				 */

	int n_erased_blocks;
	int alloc_block;	/* Current block being allocated off */
	u32 alloc_page;
	int alloc_block_finder;	/* Used to search for next allocation block */

	/* Object and Tnode memory management */
	void *allocator;
	int n_obj;
	int n_tnodes;

	int n_hardlinks;

	struct yaffs_obj_bucket obj_bucket[YAFFS_NOBJECT_BUCKETS];
	u32 bucket_finder;

	int n_free_chunks;

	/* Garbage collection control */
	u32 *gc_cleanup_list;	/* objects to delete at the end of a GC. */
	u32 n_clean_ups;

	unsigned has_pending_prioritised_gc;	/* We think this device might
						have pending prioritised gcs */
	unsigned gc_disable;
	unsigned gc_block_finder;
	unsigned gc_dirtiest;
	unsigned gc_pages_in_use;
	unsigned gc_not_done;
	unsigned gc_block;
	unsigned gc_chunk;
	unsigned gc_skip;
	struct yaffs_summary_tags *gc_sum_tags;

	/* Special directories */
	struct yaffs_obj *root_dir;
	struct yaffs_obj *lost_n_found;

	int buffered_block;	/* Which block is buffered here? */
	int doing_buffered_block_rewrite;

	struct yaffs_cache_manager cache_mgr;

	/* Stuff for background deletion and unlinked files. */
	struct yaffs_obj *unlinked_dir;	/* Directory where unlinked and deleted
					 files live. */
	struct yaffs_obj *del_dir;	/* Directory where deleted objects are
					sent to disappear. */
	struct yaffs_obj *unlinked_deletion;	/* Current file being
							background deleted. */
	int n_deleted_files;	/* Count of files awaiting deletion; */
	int n_unlinked_files;	/* Count of unlinked files. */
	int n_bg_deletions;	/* Count of background deletions. */

	/* Temporary buffer management */
	struct yaffs_buffer temp_buffer[YAFFS_N_TEMP_BUFFERS];
	int max_temp;
	int temp_in_use;
	int unmanaged_buffer_allocs;
	int unmanaged_buffer_deallocs;

	/* yaffs2 runtime stuff */
	unsigned seq_number;	/* Sequence number of currently
					allocating block */
	unsigned oldest_dirty_seq;
	unsigned oldest_dirty_block;

	/* Block refreshing */
	int refresh_skip;	/* A skip down counter.
				 * Refresh happens when this gets to zero. */

	/* Dirty directory handling */
	struct list_head dirty_dirs;	/* List of dirty directories */

	/* Summary */
	int chunks_per_summary;
	struct yaffs_summary_tags *sum_tags;

	/* Statistics */
	u32 n_page_writes;
	u32 n_page_reads;
	u32 n_erasures;
	u32 n_bad_queries;
	u32 n_bad_markings;
	u32 n_erase_failures;
	u32 n_gc_copies;
	u32 all_gcs;
	u32 passive_gc_count;
	u32 oldest_dirty_gc_count;
	u32 n_gc_blocks;
	u32 bg_gcs;
	u32 n_retried_writes;
	u32 n_retired_blocks;
	u32 n_ecc_fixed;
	u32 n_ecc_unfixed;
	u32 n_tags_ecc_fixed;
	u32 n_tags_ecc_unfixed;
	u32 n_deletions;
	u32 n_unmarked_deletions;
	u32 refresh_count;
	u32 cache_hits;
	u32 tags_used;
	u32 summary_used;

};

/*
 * Checkpointing definitions.
 */

#define YAFFS_CHECKPOINT_VERSION	8

/* yaffs_checkpt_obj holds the definition of an object as dumped
 * by checkpointing.
 */


/*  Checkpint object bits in bitfield: offset, length */
#define CHECKPOINT_VARIANT_BITS		0, 3
#define CHECKPOINT_DELETED_BITS		3, 1
#define CHECKPOINT_SOFT_DEL_BITS	4, 1
#define CHECKPOINT_UNLINKED_BITS	5, 1
#define CHECKPOINT_FAKE_BITS		6, 1
#define CHECKPOINT_RENAME_ALLOWED_BITS	7, 1
#define CHECKPOINT_UNLINK_ALLOWED_BITS	8, 1
#define CHECKPOINT_SERIAL_BITS		9, 8

/* 检查点对象结构 - Checkpoint object structure
 * 用于检查点时转储对象定义
 * Used for dumping object definition during checkpointing
 */
struct yaffs_checkpt_obj {
	int struct_type;		/* 结构类型 - Structure type */
	u32 obj_id;			/* 对象ID - Object ID */
	u32 parent_id;			/* 父对象ID - Parent object ID */
	int hdr_chunk;			/* 对象头数据块 - Header chunk */
	u32 bit_field;			/* 位域字段 - Bit field */
	int n_data_chunks;		/* 数据块数量 - Number of data chunks */
	loff_t size_or_equiv_obj;	/* 大小或等价对象 - Size or equivalent object */
};

/* 检查点设备结构 - Checkpoint device structure
 * 保存运行时变化且必须在卸载/挂载周期中保持的设备信息
 * The CheckpointDevice structure holds the device information that changes
 * at runtime and must be preserved over unmount/mount cycles.
 */
struct yaffs_checkpt_dev {
	int struct_type;		/* 结构类型 - Structure type */
	int n_erased_blocks;		/* 已擦除块数量 - Number of erased blocks */
	int alloc_block;		/* 当前正在分配的块 - Current block being allocated off */
	u32 alloc_page;			/* 分配页 - Allocation page */
	int n_free_chunks;		/* 空闲数据块数量 - Number of free chunks */

	int n_deleted_files;		/* 等待删除的文件数量 - Count of files awaiting deletion */
	int n_unlinked_files;		/* 未链接文件数量 - Count of unlinked files */
	int n_bg_deletions;		/* 后台删除数量 - Count of background deletions */

	/* yaffs2运行时数据 - yaffs2 runtime stuff */
	unsigned seq_number;		/* 当前分配块的序列号 - Sequence number of currently allocating block */

};

/* 检查点有效性结构 - Checkpoint validity structure
 * 用于验证检查点数据的有效性
 * Used to validate checkpoint data validity
 */
struct yaffs_checkpt_validity {
	int struct_type;		/* 结构类型 - Structure type */
	u32 magic;			/* 魔数 - Magic number */
	u32 version;			/* 版本号 - Version number */
	u32 head;			/* 头部标识 - Head identifier */
};

/* 影射修复器结构 - Shadow fixer structure
 * 用于修复对象影射关系
 * Used to fix object shadow relationships
 */
struct yaffs_shadow_fixer {
	int obj_id;			/* 对象ID - Object ID */
	int shadowed_id;		/* 被影射的对象ID - Shadowed object ID */
	struct yaffs_shadow_fixer *next;/* 下一个修复器 - Next fixer */
};

/* 扩展属性修改结构 - Structure for doing xattr modifications
 * 用于执行扩展属性修改操作
 * Used for performing extended attribute modification operations
 */
struct yaffs_xattr_mod {
	int set;			/* 设置标志，如果为0则表示删除 - Set flag, if 0 then this is a deletion */
	const YCHAR *name;		/* 属性名称 - Attribute name */
	const void *data;		/* 属性数据 - Attribute data */
	int size;			/* 数据大小 - Data size */
	int flags;			/* 标志 - Flags */
	int result;			/* 操作结果 - Operation result */
};

/*----------------------- YAFFS函数 - YAFFS Functions -----------------------*/

/* 核心初始化和清理函数 - Core initialization and cleanup functions */
int yaffs_guts_initialise(struct yaffs_dev *dev);		/* 初始化YAFFS核心 - Initialize YAFFS core */
void yaffs_deinitialise(struct yaffs_dev *dev);		/* 去初始化YAFFS - Deinitialize YAFFS */
void yaffs_guts_cleanup(struct yaffs_dev *dev);		/* 清理YAFFS核心 - Cleanup YAFFS core */

/* 空间管理函数 - Space management functions */
int yaffs_get_n_free_chunks(struct yaffs_dev *dev);		/* 获取空闲数据块数量 - Get number of free chunks */

/* 对象操作函数 - Object operation functions */
int yaffs_rename_obj(struct yaffs_obj *old_dir, const YCHAR * old_name,
		     struct yaffs_obj *new_dir, const YCHAR * new_name);	/* 重命名对象 - Rename object */

int yaffs_unlink_obj(struct yaffs_obj *obj);			/* 取消链接对象 - Unlink object */

int yaffs_unlinker(struct yaffs_obj *dir, const YCHAR * name);	/* 取消链接器 - Unlinker */
int yaffs_del_obj(struct yaffs_obj *obj);			/* 删除对象 - Delete object */
struct yaffs_obj *yaffs_retype_obj(struct yaffs_obj *obj,
				   enum yaffs_obj_type type);		/* 重新设置对象类型 - Retype object */


/* 对象信息获取函数 - Object information retrieval functions */
int yaffs_get_obj_name(struct yaffs_obj *obj, YCHAR * name, int buffer_size);	/* 获取对象名称 - Get object name */
loff_t yaffs_get_obj_length(struct yaffs_obj *obj);				/* 获取对象长度 - Get object length */
int yaffs_get_obj_inode(struct yaffs_obj *obj);					/* 获取对象inode - Get object inode */
unsigned yaffs_get_obj_type(struct yaffs_obj *obj);				/* 获取对象类型 - Get object type */
int yaffs_get_obj_link_count(struct yaffs_obj *obj);				/* 获取对象链接计数 - Get object link count */

/* 文件操作函数 - File operations */
int yaffs_file_rd(struct yaffs_obj *obj, u8 * buffer, loff_t offset,
		  int n_bytes);							/* 读取文件 - Read file */
int yaffs_wr_file(struct yaffs_obj *obj, const u8 * buffer, loff_t offset,
		  int n_bytes, int write_trhrough);				/* 写入文件 - Write file */
int yaffs_resize_file(struct yaffs_obj *obj, loff_t new_size);			/* 调整文件大小 - Resize file */

struct yaffs_obj *yaffs_create_file(struct yaffs_obj *parent,
				    const YCHAR *name, u32 mode, u32 uid,
				    u32 gid);						/* 创建文件 - Create file */

int yaffs_flush_file(struct yaffs_obj *in,
		     int update_time,
		     int data_sync,
		     int discard_cache);

/* Flushing and checkpointing */
void yaffs_flush_whole_cache(struct yaffs_dev *dev, int discard);

int yaffs_checkpoint_save(struct yaffs_dev *dev);
int yaffs_checkpoint_restore(struct yaffs_dev *dev);

/* 目录操作函数 - Directory operations */
struct yaffs_obj *yaffs_create_dir(struct yaffs_obj *parent, const YCHAR *name,
				   u32 mode, u32 uid, u32 gid);			/* 创建目录 - Create directory */
struct yaffs_obj *yaffs_find_by_name(struct yaffs_obj *the_dir,
				     const YCHAR *name);			/* 按名称查找对象 - Find object by name */
struct yaffs_obj *yaffs_find_by_number(struct yaffs_dev *dev, u32 number);	/* 按编号查找对象 - Find object by number */

/* 链接操作函数 - Link operations */
struct yaffs_obj *yaffs_link_obj(struct yaffs_obj *parent, const YCHAR *name,
				 struct yaffs_obj *equiv_obj);			/* 创建硬链接 - Create hard link */

struct yaffs_obj *yaffs_get_equivalent_obj(struct yaffs_obj *obj);		/* 获取等价对象 - Get equivalent object */

/* 符号链接操作函数 - Symlink operations */
struct yaffs_obj *yaffs_create_symlink(struct yaffs_obj *parent,
				       const YCHAR *name, u32 mode, u32 uid,
				       u32 gid, const YCHAR *alias);		/* 创建符号链接 - Create symbolic link */
YCHAR *yaffs_get_symlink_alias(struct yaffs_obj *obj);			/* 获取符号链接别名 - Get symbolic link alias */

/* 特殊inode操作（FIFO、套接字和设备） - Special inodes (fifos, sockets and devices) */
struct yaffs_obj *yaffs_create_special(struct yaffs_obj *parent,
				       const YCHAR *name, u32 mode, u32 uid,
				       u32 gid, u32 rdev);			/* 创建特殊文件 - Create special file */

/* 扩展属性操作函数 - Extended attributes operations */
int yaffs_set_xattrib(struct yaffs_obj *obj, const YCHAR *name,
		      const void *value, int size, int flags);			/* 设置扩展属性 - Set extended attribute */
int yaffs_get_xattrib(struct yaffs_obj *obj, const YCHAR *name, void *value,
		      int size);						/* 获取扩展属性 - Get extended attribute */
int yaffs_list_xattrib(struct yaffs_obj *obj, char *buffer, int size);	/* 列出扩展属性 - List extended attributes */
int yaffs_remove_xattrib(struct yaffs_obj *obj, const YCHAR *name);	/* 移除扩展属性 - Remove extended attribute */

/* 特殊目录函数 - Special directories */
struct yaffs_obj *yaffs_root(struct yaffs_dev *dev);			/* 获取根目录 - Get root directory */
struct yaffs_obj *yaffs_lost_n_found(struct yaffs_dev *dev);		/* 获取lost+found目录 - Get lost+found directory */

/* 内存管理和维护函数 - Memory management and maintenance functions */
void yaffs_handle_defered_free(struct yaffs_obj *obj);			/* 处理延迟释放 - Handle deferred free */

void yaffs_update_dirty_dirs(struct yaffs_dev *dev);			/* 更新脏目录 - Update dirty directories */

int yaffs_bg_gc(struct yaffs_dev *dev, unsigned urgency);		/* 后台垃圾回收 - Background garbage collection */

/* 调试转储函数 - Debug dump */
int yaffs_dump_obj(struct yaffs_obj *obj);				/* 转储对象信息 - Dump object information */

void yaffs_guts_test(struct yaffs_dev *dev);
int yaffs_guts_ll_init(struct yaffs_dev *dev);


/* 核心文件内使用的实用函数 - A few useful functions to be used within the core files */
void yaffs_chunk_del(struct yaffs_dev *dev, int chunk_id, int mark_flash,
		     int lyn);						/* 删除数据块 - Delete chunk */
int yaffs_check_ff(u8 *buffer, int n_bytes);				/* 检查缓冲区是否全为0xFF - Check if buffer is all 0xFF */
void yaffs_handle_chunk_error(struct yaffs_dev *dev,
			      struct yaffs_block_info *bi);		/* 处理数据块错误 - Handle chunk error */

/* 临时缓冲区管理函数 - Temporary buffer management functions */
u8 *yaffs_get_temp_buffer(struct yaffs_dev *dev);			/* 获取临时缓冲区 - Get temporary buffer */
void yaffs_release_temp_buffer(struct yaffs_dev *dev, u8 *buffer);	/* 释放临时缓冲区 - Release temporary buffer */

struct yaffs_obj *yaffs_find_or_create_by_number(struct yaffs_dev *dev,
						 int number,
						 enum yaffs_obj_type type);
int yaffs_put_chunk_in_file(struct yaffs_obj *in, int inode_chunk,
			    int nand_chunk, int in_scan);
void yaffs_set_obj_name(struct yaffs_obj *obj, const YCHAR *name);
void yaffs_set_obj_name_from_oh(struct yaffs_obj *obj,
				const struct yaffs_obj_hdr *oh);
void yaffs_add_obj_to_dir(struct yaffs_obj *directory, struct yaffs_obj *obj);
YCHAR *yaffs_clone_str(const YCHAR *str);
void yaffs_link_fixup(struct yaffs_dev *dev, struct list_head *hard_list);
void yaffs_block_became_dirty(struct yaffs_dev *dev, int block_no);
int yaffs_update_oh(struct yaffs_obj *in, const YCHAR *name,
		    int force, int is_shrink, int shadows,
		    struct yaffs_xattr_mod *xop);
void yaffs_handle_shadowed_obj(struct yaffs_dev *dev, int obj_id,
			       int backward_scanning);
int yaffs_check_alloc_available(struct yaffs_dev *dev, int n_chunks);
struct yaffs_tnode *yaffs_get_tnode(struct yaffs_dev *dev);
struct yaffs_tnode *yaffs_add_find_tnode_0(struct yaffs_dev *dev,
					   struct yaffs_file_var *file_struct,
					   u32 chunk_id,
					   struct yaffs_tnode *passed_tn);

int yaffs_do_file_wr(struct yaffs_obj *in, const u8 *buffer, loff_t offset,
		     int n_bytes, int write_trhrough);
void yaffs_resize_file_down(struct yaffs_obj *obj, loff_t new_size);
void yaffs_skip_rest_of_block(struct yaffs_dev *dev);

int yaffs_count_free_chunks(struct yaffs_dev *dev);

struct yaffs_tnode *yaffs_find_tnode_0(struct yaffs_dev *dev,
				       struct yaffs_file_var *file_struct,
				       u32 chunk_id);

u32 yaffs_get_group_base(struct yaffs_dev *dev, struct yaffs_tnode *tn,
			 unsigned pos);

int yaffs_is_non_empty_dir(struct yaffs_obj *obj);

int yaffs_guts_format_dev(struct yaffs_dev *dev);

void yaffs_addr_to_chunk(struct yaffs_dev *dev, loff_t addr,
				int *chunk_out, u32 *offset_out);
/*
 * Marshalling functions to get loff_t file sizes into and out of
 * object headers.
 */
void yaffs_oh_size_load(struct yaffs_dev *dev, struct yaffs_obj_hdr *oh,
			loff_t fsize, int do_endian);
loff_t yaffs_oh_to_size(struct yaffs_dev *dev, struct yaffs_obj_hdr *oh,
			int do_endian);
loff_t yaffs_max_file_size(struct yaffs_dev *dev);


/* yaffs_wr_data_obj needs to be exposed to allow the cache to access it. */
int yaffs_wr_data_obj(struct yaffs_obj *in, int inode_chunk,
			     const u8 *buffer, int n_bytes, int use_reserve);

/*
 * Debug function to count number of blocks in each state
 * NB Needs to be called with correct number of integers
 */

void yaffs_count_blocks_by_state(struct yaffs_dev *dev, int bs[10]);

int yaffs_find_chunk_in_file(struct yaffs_obj *in, int inode_chunk,
				    struct yaffs_ext_tags *tags);

/*
 *Time marshalling functions
 */

YTIME_T yaffs_oh_ctime_fetch(struct yaffs_obj_hdr *oh);
YTIME_T yaffs_oh_mtime_fetch(struct yaffs_obj_hdr *oh);
YTIME_T yaffs_oh_atime_fetch(struct yaffs_obj_hdr *oh);

void yaffs_oh_ctime_load(struct yaffs_obj *obj, struct yaffs_obj_hdr *oh);
void yaffs_oh_mtime_load(struct yaffs_obj *obj, struct yaffs_obj_hdr *oh);
void yaffs_oh_atime_load(struct yaffs_obj *obj, struct yaffs_obj_hdr *oh);

/*
 * 如果使用32位LOFF_T，请定义LOFF_T_32_BIT
 * Define LOFF_T_32_BIT if a 32-bit LOFF_T is being used.
 * 如果设置错误也不严重 - 你可能只会得到一些警告
 * Not serious if you get this wrong - you might just get some warnings.
 */

#ifdef  LOFF_T_32_BIT
/* 32位系统的文件大小处理宏 - File size handling macros for 32-bit systems */
#define FSIZE_LOW(fsize) (fsize)			/* 获取文件大小低位 - Get low part of file size */
#define FSIZE_HIGH(fsize) 0				/* 获取文件大小高位（32位系统为0） - Get high part of file size (0 for 32-bit) */
#define FSIZE_COMBINE(high, low) (low)			/* 组合高低位文件大小 - Combine high and low parts of file size */
#else
/* 64位系统的文件大小处理宏 - File size handling macros for 64-bit systems */
#define FSIZE_LOW(fsize) ((fsize) & 0xffffffff)		/* 获取文件大小低32位 - Get low 32 bits of file size */
#define FSIZE_HIGH(fsize)(((fsize) >> 32) & 0xffffffff)	/* 获取文件大小高32位 - Get high 32 bits of file size */
#define FSIZE_COMBINE(high, low) ((((loff_t) (high)) << 32) | \
					(((loff_t) (low)) & 0xFFFFFFFF))	/* 组合高低32位为64位文件大小 - Combine high and low 32 bits to 64-bit file size */
#endif


#endif
