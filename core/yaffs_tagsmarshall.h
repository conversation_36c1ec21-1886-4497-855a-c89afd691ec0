/*
 * YAFFS: Yet another Flash File System . A NAND-flash specific file system.
 * YAFFS: 又一个闪存文件系统 - 专为NAND闪存设计的文件系统
 *
 * Copyright (C) 2002-2018 Aleph One Ltd.
 *
 * Created by <PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU Lesser General Public License version 2.1 as
 * published by the Free Software Foundation.
 *
 * Note: Only YAFFS headers are LGPL, YAFFS C code is covered by GPL.
 */

/*
 * YAFFS标签编组头文件 - YAFFS Tags Marshall Header
 *
 * 此模块负责标签数据的序列化和反序列化处理
 * This module handles serialization and deserialization of tag data
 */

#ifndef __YAFFS_TAGSMARSHALL_H__
#define __YAFFS_TAGSMARSHALL_H__

#include "yaffs_guts.h"

/* 标签编组安装函数 - Tags marshall installation function */
void yaffs_tags_marshall_install(struct yaffs_dev *dev);		/* 安装标签编组支持 - Install tags marshall support */

#endif
