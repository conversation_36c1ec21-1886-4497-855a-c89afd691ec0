/*
 * YAFFS: Yet another Flash File System . A NAND-flash specific file system.
 * YAFFS: 又一个闪存文件系统 - 专为NAND闪存设计的文件系统
 *
 * Copyright (C) 2002-2018 Aleph One Ltd.
 *
 * Created by <PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU Lesser General Public License version 2.1 as
 * published by the Free Software Foundation.
 *
 * Note: Only YAFFS headers are LGPL, YAFFS C code is covered by GPL.
 */

/*
 * YAFFS摘要管理头文件 - YAFFS Summary Management Header
 *
 * 此模块提供YAFFS块摘要信息的管理功能，用于加速文件系统扫描
 * This module provides management functionality for YAFFS block summary information to accelerate filesystem scanning
 */

#ifndef __YAFFS_SUMMARY_H__
#define __YAFFS_SUMMARY_H__

#include "yaffs_packedtags2.h"

/* 摘要系统初始化/清理函数 - Summary system init/deinit functions */
int yaffs_summary_init(struct yaffs_dev *dev);				/* 初始化摘要系统 - Initialize summary system */
void yaffs_summary_deinit(struct yaffs_dev *dev);			/* 清理摘要系统 - Deinitialize summary system */

/* 摘要数据操作函数 - Summary data operation functions */
int yaffs_summary_add(struct yaffs_dev *dev,				/* 添加摘要条目 - Add summary entry */
			struct yaffs_ext_tags *tags,
			int chunk_in_block);
int yaffs_summary_fetch(struct yaffs_dev *dev,				/* 获取摘要条目 - Fetch summary entry */
			struct yaffs_ext_tags *tags,
			int chunk_in_block);
int yaffs_summary_read(struct yaffs_dev *dev,				/* 读取块摘要 - Read block summary */
			struct yaffs_summary_tags *st,
			int blk);
void yaffs_summary_gc(struct yaffs_dev *dev, int blk);			/* 摘要垃圾回收 - Summary garbage collection */

#endif
