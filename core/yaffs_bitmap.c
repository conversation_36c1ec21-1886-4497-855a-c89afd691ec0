/*
 * YAFFS: Yet Another Flash File System. A NAND-flash specific file system.
 * YAFFS: 又一个闪存文件系统 - 专为NAND闪存设计的文件系统
 *
 * Copyright (C) 2002-2018 Aleph One Ltd.
 *
 * Created by <PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License version 2 as
 * published by the Free Software Foundation.
 */

/*
 * YAFFS位图操作实现 - YAFFS Bitmap Operations Implementation
 *
 * 此文件实现了YAFFS数据块位图的操作功能
 * This file implements bitmap operations for YAFFS chunk management
 */

#include "yaffs_bitmap.h"
#include "yaffs_trace.h"

/*
 * 数据块位图操作 - Chunk bitmap manipulations
 */

/*
 * 获取块位图指针 - Get block bitmap pointer
 * 返回指向指定块的位图数据的指针
 * Return pointer to bitmap data for specified block
 */
static inline u8 *yaffs_block_bits(struct yaffs_dev *dev, int blk)
{
	/* 验证块号有效性 - Verify block number validity */
	if (blk < (int)dev->internal_start_block ||
	    blk > (int)dev->internal_end_block) {
		yaffs_trace(YAFFS_TRACE_ERROR,
			"BlockBits block %d is not valid",
			blk);
		BUG();	/* 无效块号 - Invalid block number */
	}
	/* 计算并返回块位图指针 - Calculate and return block bitmap pointer */
	return dev->chunk_bits +
	    (dev->chunk_bit_stride * (blk - dev->internal_start_block));
}

/*
 * 验证数据块位ID - Verify chunk bit ID
 * 检查块号和数据块号的有效性
 * Check validity of block number and chunk number
 */
void yaffs_verify_chunk_bit_id(struct yaffs_dev *dev, int blk, int chunk)
{
	if (blk < (int)dev->internal_start_block ||
	    blk > (int)dev->internal_end_block ||
	    chunk < 0 || chunk >= (int)dev->param.chunks_per_block) {
		yaffs_trace(YAFFS_TRACE_ERROR,
			"Chunk Id (%d:%d) invalid",
			blk, chunk);
		BUG();	/* 无效的数据块ID - Invalid chunk ID */
	}
}

/*
 * 清除块中所有数据块位 - Clear all chunk bits in block
 * 将指定块的所有数据块位设置为0
 * Set all chunk bits in specified block to 0
 */
void yaffs_clear_chunk_bits(struct yaffs_dev *dev, int blk)
{
	u8 *blk_bits = yaffs_block_bits(dev, blk);

	memset(blk_bits, 0, dev->chunk_bit_stride);	/* 清零位图 - Clear bitmap */
}

/*
 * 清除指定数据块位 - Clear specific chunk bit
 * 将指定数据块的位设置为0
 * Set specified chunk bit to 0
 */
void yaffs_clear_chunk_bit(struct yaffs_dev *dev, int blk, int chunk)
{
	u8 *blk_bits = yaffs_block_bits(dev, blk);

	yaffs_verify_chunk_bit_id(dev, blk, chunk);	/* 验证ID有效性 - Verify ID validity */
	blk_bits[chunk / 8] &= ~(1 << (chunk & 7));	/* 清除对应位 - Clear corresponding bit */
}

/*
 * 设置指定数据块位 - Set specific chunk bit
 * 将指定数据块的位设置为1
 * Set specified chunk bit to 1
 */
void yaffs_set_chunk_bit(struct yaffs_dev *dev, int blk, int chunk)
{
	u8 *blk_bits = yaffs_block_bits(dev, blk);

	yaffs_verify_chunk_bit_id(dev, blk, chunk);	/* 验证ID有效性 - Verify ID validity */
	blk_bits[chunk / 8] |= (1 << (chunk & 7));	/* 设置对应位 - Set corresponding bit */
}

/*
 * 检查指定数据块位 - Check specific chunk bit
 * 返回指定数据块位的状态（0或1）
 * Return the state of specified chunk bit (0 or 1)
 */
int yaffs_check_chunk_bit(struct yaffs_dev *dev, int blk, int chunk)
{
	u8 *blk_bits = yaffs_block_bits(dev, blk);

	yaffs_verify_chunk_bit_id(dev, blk, chunk);	/* 验证ID有效性 - Verify ID validity */
	return (blk_bits[chunk / 8] & (1 << (chunk & 7))) ? 1 : 0;	/* 返回位状态 - Return bit state */
}

int yaffs_still_some_chunks(struct yaffs_dev *dev, int blk)
{
	u8 *blk_bits = yaffs_block_bits(dev, blk);
	int i;

	for (i = 0; i < dev->chunk_bit_stride; i++) {
		if (*blk_bits)
			return 1;
		blk_bits++;
	}
	return 0;
}

int yaffs_count_chunk_bits(struct yaffs_dev *dev, int blk)
{
	u8 *blk_bits = yaffs_block_bits(dev, blk);
	int i;
	int n = 0;

	for (i = 0; i < dev->chunk_bit_stride; i++, blk_bits++)
		n += hweight8(*blk_bits);

	return n;
}
