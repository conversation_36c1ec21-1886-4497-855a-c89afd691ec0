/*
 * YAFFS: Yet another Flash File System . A NAND-flash specific file system.
 * YAFFS: 又一个闪存文件系统 - 专为NAND闪存设计的文件系统
 *
 * Copyright (C) 2002-2018 Aleph One Ltd.
 *
 * Created by <PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU Lesser General Public License version 2.1 as
 * published by the Free Software Foundation.
 *
 * Note: Only YAFFS headers are LGPL, YAFFS C code is covered by GPL.
 */

/*
 * YAFFS2特定功能头文件 - YAFFS2 Specific Functions Header
 *
 * 此模块包含YAFFS2版本特定的功能实现，包括垃圾回收、检查点和扫描功能
 * This module contains YAFFS2 version-specific functionality including garbage collection, checkpoints, and scanning
 */

#ifndef __YAFFS_YAFFS2_H__
#define __YAFFS_YAFFS2_H__

#include "yaffs_guts.h"

/* 最旧脏序列号管理函数 - Oldest dirty sequence number management functions */
void yaffs_calc_oldest_dirty_seq(struct yaffs_dev *dev);			/* 计算最旧脏序列号 - Calculate oldest dirty sequence number */
void yaffs2_find_oldest_dirty_seq(struct yaffs_dev *dev);			/* 查找最旧脏序列号 - Find oldest dirty sequence number */
void yaffs2_clear_oldest_dirty_seq(struct yaffs_dev *dev,			/* 清除最旧脏序列号 - Clear oldest dirty sequence number */
				   struct yaffs_block_info *bi);
void yaffs2_update_oldest_dirty_seq(struct yaffs_dev *dev, unsigned block_no,	/* 更新最旧脏序列号 - Update oldest dirty sequence number */
				    struct yaffs_block_info *bi);

/* 垃圾回收相关函数 - Garbage collection related functions */
int yaffs_block_ok_for_gc(struct yaffs_dev *dev, struct yaffs_block_info *bi);	/* 检查块是否适合垃圾回收 - Check if block is OK for garbage collection */
u32 yaffs2_find_refresh_block(struct yaffs_dev *dev);				/* 查找需要刷新的块 - Find block that needs refresh */

/* 检查点管理函数 - Checkpoint management functions */
int yaffs2_checkpt_required(struct yaffs_dev *dev);				/* 检查是否需要检查点 - Check if checkpoint is required */
int yaffs_calc_checkpt_blocks_required(struct yaffs_dev *dev);			/* 计算检查点所需的块数 - Calculate blocks required for checkpoint */
void yaffs2_checkpt_invalidate(struct yaffs_dev *dev);				/* 使检查点失效 - Invalidate checkpoint */
int yaffs2_checkpt_save(struct yaffs_dev *dev);					/* 保存检查点 - Save checkpoint */
int yaffs2_checkpt_restore(struct yaffs_dev *dev);				/* 恢复检查点 - Restore checkpoint */

/* 文件操作和扫描函数 - File operations and scanning functions */
int yaffs2_handle_hole(struct yaffs_obj *obj, loff_t new_size);			/* 处理文件空洞 - Handle file holes */
int yaffs2_scan_backwards(struct yaffs_dev *dev);				/* 反向扫描文件系统 - Scan filesystem backwards */

#endif
