/*
 * YAFFS: Yet another Flash File System . A NAND-flash specific file system.
 * YAFFS: 又一个闪存文件系统 - 专为NAND闪存设计的文件系统
 *
 * Copyright (C) 2002-2018 Aleph One Ltd.
 *
 * Created by <PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU Lesser General Public License version 2.1 as
 * published by the Free Software Foundation.
 *
 * Note: Only YAFFS headers are LGPL, YAFFS C code is covered by GPL.
 */

/*
 * YAFFS检查点读写头文件 - YAFFS Checkpoint Read/Write Header
 *
 * 此模块提供YAFFS2检查点数据的读写功能
 * This module provides read/write functionality for YAFFS2 checkpoint data
 */

#ifndef __YAFFS_CHECKPTRW_H__
#define __YAFFS_CHECKPTRW_H__

#include "yaffs_guts.h"

/* 检查点操作函数 - Checkpoint operation functions */
int yaffs2_checkpt_open(struct yaffs_dev *dev, int writing);		/* 打开检查点，指定读写模式 - Open checkpoint with read/write mode */

int yaffs2_checkpt_wr(struct yaffs_dev *dev, const void *data, int n_bytes);	/* 写入检查点数据 - Write checkpoint data */

int yaffs2_checkpt_rd(struct yaffs_dev *dev, void *data, int n_bytes);		/* 读取检查点数据 - Read checkpoint data */

int yaffs2_get_checkpt_sum(struct yaffs_dev *dev, u32 * sum);			/* 获取检查点校验和 - Get checkpoint checksum */

int yaffs_checkpt_close(struct yaffs_dev *dev);				/* 关闭检查点 - Close checkpoint */

int yaffs2_checkpt_invalidate_stream(struct yaffs_dev *dev);			/* 使检查点流失效 - Invalidate checkpoint stream */

#endif
