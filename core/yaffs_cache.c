/*
 * YAFFS: Yet Another Flash File System. A NAND-flash specific file system.
 * YAFFS: 又一个闪存文件系统 - 专为NAND闪存设计的文件系统
 *
 * Copyright (C) 2002-2018 Aleph One Ltd.
 *
 * Created by <PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License version 2 as
 * published by the Free Software Foundation.
 */

/*
 * YAFFS缓存管理实现 - YAFFS Cache Management Implementation
 *
 * 此文件实现了YAFFS的短操作缓存系统
 * This file implements YAFFS short operation cache system
 */

#include "yaffs_cache.h"

/*------------------------ 短操作缓存 - Short Operations Cache ------------------------------
 *   In many situations where there is no high level buffering  a lot of
 *   reads might be short sequential reads, and a lot of writes may be short
 *   sequential writes. eg. scanning/writing a jpeg file.
 *   In these cases, a short read/write cache can provide a huge perfomance
 *   benefit with dumb-as-a-rock code.
 *   In Linux, the page cache provides read buffering and the short op cache
 *   provides write buffering.
 *
 *   There are a small number (~10) of cache chunks per device so that we don't
 *   need a very intelligent search.
 *
 *   在许多没有高级缓冲的情况下，大量读取可能是短顺序读取，
 *   大量写入可能是短顺序写入。例如扫描/写入jpeg文件。
 *   在这些情况下，短读写缓存可以通过简单的代码提供巨大的性能优势。
 *   在Linux中，页缓存提供读缓冲，短操作缓存提供写缓冲。
 *
 *   每个设备有少量（约10个）缓存块，因此我们不需要非常智能的搜索。
 */

/*
 * 检查对象缓存是否脏 - Check if object cache is dirty
 * 遍历所有缓存项，检查指定对象是否有脏缓存
 * Traverse all cache entries to check if specified object has dirty cache
 */
int yaffs_obj_cache_dirty(struct yaffs_obj *obj)
{
	struct yaffs_dev *dev = obj->my_dev;
	int i;
	struct yaffs_cache_manager *mgr = &dev->cache_mgr;

	/* 遍历所有缓存项 - Traverse all cache entries */
	for (i = 0; i < mgr->n_caches; i++) {
		struct yaffs_cache *cache = &mgr->cache[i];

		/* 如果找到属于该对象的脏缓存 - If found dirty cache belonging to this object */
		if (cache->object == obj && cache->dirty)
			return 1;
	}

	return 0;	/* 没有脏缓存 - No dirty cache */
}

/*
 * 刷新单个缓存项 - Flush single cache entry
 * 将脏缓存写回存储设备，可选择丢弃缓存
 * Write dirty cache back to storage device, optionally discard cache
 */
void yaffs_flush_single_cache(struct yaffs_cache *cache, int discard)
{
	if (!cache || cache->locked)
		return;	/* 缓存无效或被锁定 - Cache invalid or locked */

	/* 如果需要，写出并释放 - Write it out and free it up if need be */
	if (cache->dirty) {
		yaffs_wr_data_obj(cache->object,	/* 写入数据到对象 - Write data to object */
				  cache->chunk_id,
				  cache->data,
				  cache->n_bytes,
				  1);

		cache->dirty = 0;	/* 清除脏标志 - Clear dirty flag */
	}

	if (discard)
		cache->object = NULL;	/* 丢弃缓存，清除对象引用 - Discard cache, clear object reference */
}

/*
 * 刷新文件缓存 - Flush file cache
 * 刷新属于指定对象的所有缓存项
 * Flush all cache entries belonging to specified object
 */
void yaffs_flush_file_cache(struct yaffs_obj *obj, int discard)
{
	struct yaffs_dev *dev = obj->my_dev;
	int i;
	struct yaffs_cache_manager *mgr = &dev->cache_mgr;

	if (mgr->n_caches < 1)
		return;	/* 没有缓存 - No caches */

	/* 查找属于此对象的数据块并刷新它们 - Find the chunks for this object and flush them */
	for (i = 0; i < mgr->n_caches; i++) {
		struct yaffs_cache *cache = &mgr->cache[i];

		if (cache->object == obj)
			yaffs_flush_single_cache(cache, discard);
	}

}


void yaffs_flush_whole_cache(struct yaffs_dev *dev, int discard)
{
	struct yaffs_cache_manager *mgr = &dev->cache_mgr;
	struct yaffs_obj *obj;
	int i;

	/* Find a dirty object in the cache and flush it...
	 * until there are no further dirty objects.
	 */
	do {
		obj = NULL;
		for (i = 0; i < mgr->n_caches && !obj; i++) {
			struct yaffs_cache *cache = &mgr->cache[i];
			if (cache->object && cache->dirty)
				obj = cache->object;
		}
		if (obj)
			yaffs_flush_file_cache(obj, discard);
	} while (obj);

}

/* Grab us an unused cache chunk for use.
 * First look for an empty one.
 * Then look for the least recently used non-dirty one.
 * Then look for the least recently used dirty one...., flush and look again.
 */
static struct yaffs_cache *yaffs_grab_chunk_worker(struct yaffs_dev *dev)
{
	struct yaffs_cache_manager *mgr = &dev->cache_mgr;
	int i;

	for (i = 0; i < mgr->n_caches; i++) {
		struct yaffs_cache *cache = &mgr->cache[i];
		if (!cache->object)
			return cache;
	}

	return NULL;
}

struct yaffs_cache *yaffs_grab_chunk_cache(struct yaffs_dev *dev)
{
	struct yaffs_cache_manager *mgr = &dev->cache_mgr;
	struct yaffs_cache *cache;
	int usage;
	int i;

	if (mgr->n_caches < 1)
		return NULL;

	/* First look for an unused cache */

	cache = yaffs_grab_chunk_worker(dev);

	if (cache)
		return cache;

	/*
	 * Thery were all in use.
	 * Find the LRU cache and flush it if it is dirty.
	 */

	usage = -1;
	cache = NULL;

	for (i = 0; i < mgr->n_caches; i++) {
		struct yaffs_cache *this_cache = &mgr->cache[i];

		if (this_cache->object &&
		    !this_cache->locked &&
		    (this_cache->last_use < usage || !cache)) {
				usage = this_cache->last_use;
				cache = this_cache;
		}
	}

#if 1
	yaffs_flush_single_cache(cache, 1);
#else
	yaffs_flush_file_cache(cache->object, 1);
	cache = yaffs_grab_chunk_worker(dev);
#endif

	return cache;
}

/* Find a cached chunk */
struct yaffs_cache *yaffs_find_chunk_cache(const struct yaffs_obj *obj,
						  int chunk_id)
{
	struct yaffs_dev *dev = obj->my_dev;
	struct yaffs_cache_manager *mgr = &dev->cache_mgr;
	int i;

	if (mgr->n_caches < 1)
		return NULL;

	for (i = 0; i < mgr->n_caches; i++) {
		struct yaffs_cache *cache = &mgr->cache[i];

		if (cache->object == obj &&
		    cache->chunk_id == chunk_id) {
			dev->cache_hits++;
			return cache;
		}
	}
	return NULL;
}

/* Mark the chunk for the least recently used algorithym */
void yaffs_use_cache(struct yaffs_dev *dev, struct yaffs_cache *cache,
			    int is_write)
{
	struct yaffs_cache_manager *mgr = &dev->cache_mgr;
	int i;

	if (mgr->n_caches < 1)
		return;

	if (mgr->cache_last_use < 0 ||
		mgr->cache_last_use > 100000000) {
		/* Reset the cache usages */
		for (i = 1; i < mgr->n_caches; i++)
			mgr->cache[i].last_use = 0;

		mgr->cache_last_use = 0;
	}
	mgr->cache_last_use++;
	cache->last_use = mgr->cache_last_use;

	if (is_write)
		cache->dirty = 1;
}

/* Invalidate a single cache page.
 * Do this when a whole page gets written,
 * ie the short cache for this page is no longer valid.
 */
void yaffs_invalidate_chunk_cache(struct yaffs_obj *object, int chunk_id)
{
	struct yaffs_cache *cache;

	cache = yaffs_find_chunk_cache(object, chunk_id);
	if (cache)
		cache->object = NULL;
}

/* Invalidate all the cache pages associated with this object
 * Do this whenever the file is deleted or resized.
 */
void yaffs_invalidate_file_cache(struct yaffs_obj *in)
{
	int i;
	struct yaffs_dev *dev = in->my_dev;
	struct yaffs_cache_manager *mgr = &dev->cache_mgr;

	/* Invalidate it. */
	for (i = 0; i < mgr->n_caches; i++) {
		struct yaffs_cache *cache = &mgr->cache[i];

		if (cache->object == in)
			cache->object = NULL;
	}
}

int yaffs_count_dirty_caches(struct yaffs_dev *dev)
{
	int n_dirty;
	int i;
	struct yaffs_cache_manager *mgr = &dev->cache_mgr;

	for (n_dirty= 0, i = 0; i < mgr->n_caches; i++) {
		if (mgr->cache[i].dirty)
			n_dirty++;
	}

	return n_dirty;
}

int yaffs_cache_init(struct yaffs_dev *dev)
{
	struct yaffs_cache_manager *mgr = &dev->cache_mgr;
	int init_failed = 0;

	if (dev->param.n_caches > YAFFS_MAX_SHORT_OP_CACHES)
			dev->param.n_caches = YAFFS_MAX_SHORT_OP_CACHES;

	mgr->n_caches = dev->param.n_caches;
	if (mgr->n_caches > 0) {
		int i;
		void *buf;
		u32 cache_bytes =
		    mgr->n_caches * sizeof(struct yaffs_cache);



		mgr->cache = kmalloc(cache_bytes, GFP_NOFS);

		buf = (u8 *) mgr->cache;

		if (mgr->cache)
			memset(mgr->cache, 0, cache_bytes);

		for (i = 0; i < mgr->n_caches && buf; i++) {
			struct yaffs_cache *cache = &mgr->cache[i];

			cache->object = NULL;
			cache->last_use = 0;
			cache->dirty = 0;
			cache->data = buf =
			    kmalloc(dev->param.total_bytes_per_chunk, GFP_NOFS);
		}
		if (!buf)
			init_failed = 1;

		mgr->cache_last_use = 0;
	}

	return init_failed ? -1 : 0;
}

void yaffs_cache_deinit(struct yaffs_dev *dev)
{
	struct yaffs_cache_manager *mgr = &dev->cache_mgr;
	int i;

	if (mgr->n_caches < 1 || !mgr->cache)
		return;

	for (i = 0; i < mgr->n_caches; i++) {

		struct yaffs_cache *cache = &mgr->cache[i];
		kfree(cache->data);
		cache->data = NULL;
	}

	kfree(mgr->cache);
	mgr->cache = NULL;
}
