{"type": "float-area", "_widgets": [[{"widget": {"constructionOptions": {"factoryId": "code-editor-opener", "options": {"kind": "navigatable", "uri": "core/yaffs_guts.h", "floatAreaId": "eca3572c-39bf-4825-95cd-cd6fe2f7ee4b"}}, "innerWidgetState": {"cursorState": [{"inSelectionMode": true, "selectionStart": {"lineNumber": 1000, "column": 16}, "position": {"lineNumber": 1000, "column": 26}}], "viewState": {"scrollLeft": 0, "firstPosition": {"lineNumber": 988, "column": 1}, "firstPositionDeltaTop": -6}, "contributionsState": {"editor.contrib.folding": {"lineCount": 1202, "provider": "syntax", "foldedImports": false}, "editor.contrib.wordHighlighter": false}}}, "width": 600, "height": 450, "spacing": 0}], [{"widget": {"constructionOptions": {"factoryId": "code-editor-opener", "options": {"kind": "navigatable", "uri": "core/yaffs_guts.c", "floatAreaId": "eca3572c-39bf-4825-95cd-cd6fe2f7ee4b"}}, "innerWidgetState": {"cursorState": [{"inSelectionMode": false, "selectionStart": {"lineNumber": 49, "column": 21}, "position": {"lineNumber": 49, "column": 21}}], "viewState": {"scrollLeft": 0, "firstPosition": {"lineNumber": 4705, "column": 1}, "firstPositionDeltaTop": -16}, "contributionsState": {"editor.contrib.folding": {"lineCount": 5154, "provider": "syntax", "foldedImports": false}, "editor.contrib.wordHighlighter": false}}}, "width": 600, "height": 450, "spacing": 0}]], "_widths": [600, 600], "_modelData": {"connections": [{"startWord": {"uri": "core/yaffs_guts.c", "lineNumber": 49, "startColumn": 13, "endLineNumber": 49, "endColumn": 32, "word": "yaffs_fix_null_name", "x": 747.800048828125, "y": 59.98125076293945, "hide": true, "height": 1, "width": 1}, "endWord": {"uri": "core/yaffs_guts.c", "lineNumber": 4395, "startColumn": 13, "endLineNumber": 4395, "endColumn": 32, "word": "", "x": 747.800048828125, "y": 59.98125076293945, "hide": true, "height": 1, "width": 1}, "id": "7a28fffb-ae10-435c-a179-0d3c181dc2f2", "startAnchor": {"x": 747.800048828125, "y": 59.98125076293945}, "endAnchor": {"x": 747.800048828125, "y": 59.98125076293945}, "startControl": {"x": 747.800048828125, "y": 59.98125076293945}, "endControl": {"x": 747.800048828125, "y": 59.98125076293945}, "arrow": {"angle": 0, "x": 747.800048828125, "y": 59.98125076293945}}, {"startWord": {"uri": "core/yaffs_guts.h", "lineNumber": 1000, "startColumn": 5, "endLineNumber": 1000, "endColumn": 26, "word": "yaffs_guts_initialise", "x": 120.79375076293945, "y": 269.98125076293945, "hide": false, "height": 18, "width": 152}, "endWord": {"uri": "core/yaffs_guts.c", "lineNumber": 4719, "startColumn": 5, "endLineNumber": 4719, "endColumn": 26, "word": "", "x": 798.800048828125, "y": 295.98125076293945, "hide": false, "height": 18, "width": 151}, "id": "ef2cf75d-1a12-42d4-a63d-979873d08a51", "startAnchor": {"x": 272.79375076293945, "y": 281.89782881637313}, "endAnchor": {"x": 798.800048828125, "y": 302.0838607230152}, "startControl": {"x": 304.7702132466736, "y": 281.89782881637313}, "endControl": {"x": 754.8324129129905, "y": 302.0838607230152}, "arrow": {"angle": 0, "x": 786.800048828125, "y": 302.0838607230152}}], "annotations": [], "highlights": []}}