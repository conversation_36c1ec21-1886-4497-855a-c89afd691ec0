{"bottomPanel": {"config": {"main": {"type": "tab-area", "widgets": [{"constructionOptions": {"factoryId": "problems"}, "innerWidgetState": {}}], "currentIndex": 0}}, "expanded": false}, "leftPanel": {"type": "sidepanel", "items": [{"widget": {"constructionOptions": {"factoryId": "explorer-view-container"}, "innerWidgetState": {"parts": [{"widget": {"constructionOptions": {"factoryId": "files"}, "innerWidgetState": {"decorations": {}, "root": {"id": "WorkspaceNodeId", "name": "WorkspaceNode", "children": [{"id": "/Users/<USER>/Develop/Repositories/yaffs2", "uri": "file:///Users/<USER>/Develop/Repositories/yaffs2", "expanded": true, "selected": false, "children": [{"id": "/Users/<USER>/Develop/Repositories/yaffs2:/Users/<USER>/Develop/Repositories/yaffs2/.codemap", "uri": "file:///Users/<USER>/Develop/Repositories/yaffs2/.codemap", "expanded": false, "selected": false, "children": [], "stat": {"type": 2}}, {"id": "/Users/<USER>/Develop/Repositories/yaffs2:/Users/<USER>/Develop/Repositories/yaffs2/.idea", "uri": "file:///Users/<USER>/Develop/Repositories/yaffs2/.idea", "expanded": false, "selected": false, "children": [], "stat": {"type": 2}}, {"id": "/Users/<USER>/Develop/Repositories/yaffs2:/Users/<USER>/Develop/Repositories/yaffs2/.vscode", "uri": "file:///Users/<USER>/Develop/Repositories/yaffs2/.vscode", "expanded": false, "selected": false, "children": [], "stat": {"type": 2}}, {"id": "/Users/<USER>/Develop/Repositories/yaffs2:/Users/<USER>/Develop/Repositories/yaffs2/core", "uri": "file:///Users/<USER>/Develop/Repositories/yaffs2/core", "expanded": true, "selected": false, "children": [{"id": "/Users/<USER>/Develop/Repositories/yaffs2:/Users/<USER>/Develop/Repositories/yaffs2/core/yaffs_allocator.c", "uri": "file:///Users/<USER>/Develop/Repositories/yaffs2/core/yaffs_allocator.c", "selected": false, "stat": {"type": 1}}, {"id": "/Users/<USER>/Develop/Repositories/yaffs2:/Users/<USER>/Develop/Repositories/yaffs2/core/yaffs_allocator.h", "uri": "file:///Users/<USER>/Develop/Repositories/yaffs2/core/yaffs_allocator.h", "selected": false, "stat": {"type": 1}}, {"id": "/Users/<USER>/Develop/Repositories/yaffs2:/Users/<USER>/Develop/Repositories/yaffs2/core/yaffs_attribs.c", "uri": "file:///Users/<USER>/Develop/Repositories/yaffs2/core/yaffs_attribs.c", "selected": false, "stat": {"type": 1}}, {"id": "/Users/<USER>/Develop/Repositories/yaffs2:/Users/<USER>/Develop/Repositories/yaffs2/core/yaffs_attribs.h", "uri": "file:///Users/<USER>/Develop/Repositories/yaffs2/core/yaffs_attribs.h", "selected": false, "stat": {"type": 1}}, {"id": "/Users/<USER>/Develop/Repositories/yaffs2:/Users/<USER>/Develop/Repositories/yaffs2/core/yaffs_bitmap.c", "uri": "file:///Users/<USER>/Develop/Repositories/yaffs2/core/yaffs_bitmap.c", "selected": false, "stat": {"type": 1}}, {"id": "/Users/<USER>/Develop/Repositories/yaffs2:/Users/<USER>/Develop/Repositories/yaffs2/core/yaffs_bitmap.h", "uri": "file:///Users/<USER>/Develop/Repositories/yaffs2/core/yaffs_bitmap.h", "selected": false, "stat": {"type": 1}}, {"id": "/Users/<USER>/Develop/Repositories/yaffs2:/Users/<USER>/Develop/Repositories/yaffs2/core/yaffs_cache.c", "uri": "file:///Users/<USER>/Develop/Repositories/yaffs2/core/yaffs_cache.c", "selected": false, "stat": {"type": 1}}, {"id": "/Users/<USER>/Develop/Repositories/yaffs2:/Users/<USER>/Develop/Repositories/yaffs2/core/yaffs_cache.h", "uri": "file:///Users/<USER>/Develop/Repositories/yaffs2/core/yaffs_cache.h", "selected": false, "stat": {"type": 1}}, {"id": "/Users/<USER>/Develop/Repositories/yaffs2:/Users/<USER>/Develop/Repositories/yaffs2/core/yaffs_checkptrw.c", "uri": "file:///Users/<USER>/Develop/Repositories/yaffs2/core/yaffs_checkptrw.c", "selected": false, "stat": {"type": 1}}, {"id": "/Users/<USER>/Develop/Repositories/yaffs2:/Users/<USER>/Develop/Repositories/yaffs2/core/yaffs_checkptrw.h", "uri": "file:///Users/<USER>/Develop/Repositories/yaffs2/core/yaffs_checkptrw.h", "selected": false, "stat": {"type": 1}}, {"id": "/Users/<USER>/Develop/Repositories/yaffs2:/Users/<USER>/Develop/Repositories/yaffs2/core/yaffs_ecc.c", "uri": "file:///Users/<USER>/Develop/Repositories/yaffs2/core/yaffs_ecc.c", "selected": false, "stat": {"type": 1}}, {"id": "/Users/<USER>/Develop/Repositories/yaffs2:/Users/<USER>/Develop/Repositories/yaffs2/core/yaffs_ecc.h", "uri": "file:///Users/<USER>/Develop/Repositories/yaffs2/core/yaffs_ecc.h", "selected": false, "stat": {"type": 1}}, {"id": "/Users/<USER>/Develop/Repositories/yaffs2:/Users/<USER>/Develop/Repositories/yaffs2/core/yaffs_endian.c", "uri": "file:///Users/<USER>/Develop/Repositories/yaffs2/core/yaffs_endian.c", "selected": false, "stat": {"type": 1}}, {"id": "/Users/<USER>/Develop/Repositories/yaffs2:/Users/<USER>/Develop/Repositories/yaffs2/core/yaffs_endian.h", "uri": "file:///Users/<USER>/Develop/Repositories/yaffs2/core/yaffs_endian.h", "selected": false, "stat": {"type": 1}}, {"id": "/Users/<USER>/Develop/Repositories/yaffs2:/Users/<USER>/Develop/Repositories/yaffs2/core/yaffs_getblockinfo.h", "uri": "file:///Users/<USER>/Develop/Repositories/yaffs2/core/yaffs_getblockinfo.h", "selected": false, "stat": {"type": 1}}, {"id": "/Users/<USER>/Develop/Repositories/yaffs2:/Users/<USER>/Develop/Repositories/yaffs2/core/yaffs_guts.c", "uri": "file:///Users/<USER>/Develop/Repositories/yaffs2/core/yaffs_guts.c", "selected": false, "focus": false, "stat": {"type": 1}}, {"id": "/Users/<USER>/Develop/Repositories/yaffs2:/Users/<USER>/Develop/Repositories/yaffs2/core/yaffs_guts.h", "uri": "file:///Users/<USER>/Develop/Repositories/yaffs2/core/yaffs_guts.h", "selected": false, "stat": {"type": 1}}, {"id": "/Users/<USER>/Develop/Repositories/yaffs2:/Users/<USER>/Develop/Repositories/yaffs2/core/yaffs_nameval.c", "uri": "file:///Users/<USER>/Develop/Repositories/yaffs2/core/yaffs_nameval.c", "selected": false, "stat": {"type": 1}}, {"id": "/Users/<USER>/Develop/Repositories/yaffs2:/Users/<USER>/Develop/Repositories/yaffs2/core/yaffs_nameval.h", "uri": "file:///Users/<USER>/Develop/Repositories/yaffs2/core/yaffs_nameval.h", "selected": false, "stat": {"type": 1}}, {"id": "/Users/<USER>/Develop/Repositories/yaffs2:/Users/<USER>/Develop/Repositories/yaffs2/core/yaffs_nand.c", "uri": "file:///Users/<USER>/Develop/Repositories/yaffs2/core/yaffs_nand.c", "selected": false, "stat": {"type": 1}}, {"id": "/Users/<USER>/Develop/Repositories/yaffs2:/Users/<USER>/Develop/Repositories/yaffs2/core/yaffs_nand.h", "uri": "file:///Users/<USER>/Develop/Repositories/yaffs2/core/yaffs_nand.h", "selected": false, "stat": {"type": 1}}, {"id": "/Users/<USER>/Develop/Repositories/yaffs2:/Users/<USER>/Develop/Repositories/yaffs2/core/yaffs_packedtags1.c", "uri": "file:///Users/<USER>/Develop/Repositories/yaffs2/core/yaffs_packedtags1.c", "selected": false, "stat": {"type": 1}}, {"id": "/Users/<USER>/Develop/Repositories/yaffs2:/Users/<USER>/Develop/Repositories/yaffs2/core/yaffs_packedtags1.h", "uri": "file:///Users/<USER>/Develop/Repositories/yaffs2/core/yaffs_packedtags1.h", "selected": false, "stat": {"type": 1}}, {"id": "/Users/<USER>/Develop/Repositories/yaffs2:/Users/<USER>/Develop/Repositories/yaffs2/core/yaffs_packedtags2.c", "uri": "file:///Users/<USER>/Develop/Repositories/yaffs2/core/yaffs_packedtags2.c", "selected": false, "stat": {"type": 1}}, {"id": "/Users/<USER>/Develop/Repositories/yaffs2:/Users/<USER>/Develop/Repositories/yaffs2/core/yaffs_packedtags2.h", "uri": "file:///Users/<USER>/Develop/Repositories/yaffs2/core/yaffs_packedtags2.h", "selected": false, "stat": {"type": 1}}, {"id": "/Users/<USER>/Develop/Repositories/yaffs2:/Users/<USER>/Develop/Repositories/yaffs2/core/yaffs_summary.c", "uri": "file:///Users/<USER>/Develop/Repositories/yaffs2/core/yaffs_summary.c", "selected": false, "stat": {"type": 1}}, {"id": "/Users/<USER>/Develop/Repositories/yaffs2:/Users/<USER>/Develop/Repositories/yaffs2/core/yaffs_summary.h", "uri": "file:///Users/<USER>/Develop/Repositories/yaffs2/core/yaffs_summary.h", "selected": false, "stat": {"type": 1}}, {"id": "/Users/<USER>/Develop/Repositories/yaffs2:/Users/<USER>/Develop/Repositories/yaffs2/core/yaffs_tagscompat.c", "uri": "file:///Users/<USER>/Develop/Repositories/yaffs2/core/yaffs_tagscompat.c", "selected": false, "stat": {"type": 1}}, {"id": "/Users/<USER>/Develop/Repositories/yaffs2:/Users/<USER>/Develop/Repositories/yaffs2/core/yaffs_tagscompat.h", "uri": "file:///Users/<USER>/Develop/Repositories/yaffs2/core/yaffs_tagscompat.h", "selected": false, "stat": {"type": 1}}, {"id": "/Users/<USER>/Develop/Repositories/yaffs2:/Users/<USER>/Develop/Repositories/yaffs2/core/yaffs_tagsmarshall.c", "uri": "file:///Users/<USER>/Develop/Repositories/yaffs2/core/yaffs_tagsmarshall.c", "selected": false, "stat": {"type": 1}}, {"id": "/Users/<USER>/Develop/Repositories/yaffs2:/Users/<USER>/Develop/Repositories/yaffs2/core/yaffs_tagsmarshall.h", "uri": "file:///Users/<USER>/Develop/Repositories/yaffs2/core/yaffs_tagsmarshall.h", "selected": false, "stat": {"type": 1}}, {"id": "/Users/<USER>/Develop/Repositories/yaffs2:/Users/<USER>/Develop/Repositories/yaffs2/core/yaffs_trace.h", "uri": "file:///Users/<USER>/Develop/Repositories/yaffs2/core/yaffs_trace.h", "selected": false, "stat": {"type": 1}}, {"id": "/Users/<USER>/Develop/Repositories/yaffs2:/Users/<USER>/Develop/Repositories/yaffs2/core/yaffs_verify.c", "uri": "file:///Users/<USER>/Develop/Repositories/yaffs2/core/yaffs_verify.c", "selected": false, "stat": {"type": 1}}, {"id": "/Users/<USER>/Develop/Repositories/yaffs2:/Users/<USER>/Develop/Repositories/yaffs2/core/yaffs_verify.h", "uri": "file:///Users/<USER>/Develop/Repositories/yaffs2/core/yaffs_verify.h", "selected": false, "stat": {"type": 1}}, {"id": "/Users/<USER>/Develop/Repositories/yaffs2:/Users/<USER>/Develop/Repositories/yaffs2/core/yaffs_yaffs1.c", "uri": "file:///Users/<USER>/Develop/Repositories/yaffs2/core/yaffs_yaffs1.c", "selected": false, "stat": {"type": 1}}, {"id": "/Users/<USER>/Develop/Repositories/yaffs2:/Users/<USER>/Develop/Repositories/yaffs2/core/yaffs_yaffs1.h", "uri": "file:///Users/<USER>/Develop/Repositories/yaffs2/core/yaffs_yaffs1.h", "selected": false, "stat": {"type": 1}}, {"id": "/Users/<USER>/Develop/Repositories/yaffs2:/Users/<USER>/Develop/Repositories/yaffs2/core/yaffs_yaffs2.c", "uri": "file:///Users/<USER>/Develop/Repositories/yaffs2/core/yaffs_yaffs2.c", "selected": false, "stat": {"type": 1}}, {"id": "/Users/<USER>/Develop/Repositories/yaffs2:/Users/<USER>/Develop/Repositories/yaffs2/core/yaffs_yaffs2.h", "uri": "file:///Users/<USER>/Develop/Repositories/yaffs2/core/yaffs_yaffs2.h", "selected": false, "stat": {"type": 1}}], "focus": false, "stat": {"type": 2, "ctime": 1754185207646, "mtime": 1754363403466, "size": 1280}}, {"id": "/Users/<USER>/Develop/Repositories/yaffs2:/Users/<USER>/Develop/Repositories/yaffs2/direct", "uri": "file:///Users/<USER>/Develop/Repositories/yaffs2/direct", "expanded": false, "selected": false, "children": [], "stat": {"type": 2}}, {"id": "/Users/<USER>/Develop/Repositories/yaffs2:/Users/<USER>/Develop/Repositories/yaffs2/linux", "uri": "file:///Users/<USER>/Develop/Repositories/yaffs2/linux", "expanded": false, "selected": false, "children": [], "stat": {"type": 2}}, {"id": "/Users/<USER>/Develop/Repositories/yaffs2:/Users/<USER>/Develop/Repositories/yaffs2/rtems", "uri": "file:///Users/<USER>/Develop/Repositories/yaffs2/rtems", "expanded": false, "selected": false, "children": [], "stat": {"type": 2}}, {"id": "/Users/<USER>/Develop/Repositories/yaffs2:/Users/<USER>/Develop/Repositories/yaffs2/utils", "uri": "file:///Users/<USER>/Develop/Repositories/yaffs2/utils", "expanded": false, "selected": false, "children": [], "stat": {"type": 2}}, {"id": "/Users/<USER>/Develop/Repositories/yaffs2:/Users/<USER>/Develop/Repositories/yaffs2/.gitignore", "uri": "file:///Users/<USER>/Develop/Repositories/yaffs2/.gitignore", "selected": false, "stat": {"type": 1}}, {"id": "/Users/<USER>/Develop/Repositories/yaffs2:/Users/<USER>/Develop/Repositories/yaffs2/LICENSE", "uri": "file:///Users/<USER>/Develop/Repositories/yaffs2/LICENSE", "selected": false, "stat": {"type": 1}}, {"id": "/Users/<USER>/Develop/Repositories/yaffs2:/Users/<USER>/Develop/Repositories/yaffs2/README.md", "uri": "file:///Users/<USER>/Develop/Repositories/yaffs2/README.md", "selected": false, "stat": {"type": 1}}], "visible": false, "focus": false, "stat": {"type": 2, "ctime": 1754185204737, "mtime": 1754366150936, "size": 480}}], "visible": false, "selected": false}, "model": {"selection": {"selectionStack": []}}}}, "partId": "{\"factoryId\":\"files\"}", "collapsed": false, "hidden": false, "relativeSize": 1}, {"widget": {"constructionOptions": {"factoryId": "plugin-view", "options": {"id": "plugin-view:go.explorer", "viewId": "go.explorer"}}, "innerWidgetState": {"label": "go", "widgets": []}}, "partId": "{\"factoryId\":\"plugin-view\",\"options\":{\"id\":\"plugin-view:go.explorer\",\"viewId\":\"go.explorer\"}}", "collapsed": true, "hidden": true}, {"widget": {"constructionOptions": {"factoryId": "plugin-view", "options": {"id": "plugin-view:npm", "viewId": "npm"}}, "innerWidgetState": {"label": "NPM Scripts", "widgets": []}}, "partId": "{\"factoryId\":\"plugin-view\",\"options\":{\"id\":\"plugin-view:npm\",\"viewId\":\"npm\"}}", "collapsed": true, "hidden": true}], "title": {"label": "Explorer", "iconClass": "codemap-files-icon", "closeable": true}}}, "rank": 100, "expanded": true}, {"widget": {"constructionOptions": {"factoryId": "search-in-workspace"}, "innerWidgetState": {"matchCaseState": {"className": "match-case", "enabled": true, "title": "Match Case"}, "wholeWordState": {"className": "whole-word", "enabled": true, "title": "Match Whole Word"}, "regExpState": {"className": "use-regexp", "enabled": false, "title": "Use Regular Expression"}, "includeIgnoredState": {"className": "include-ignored fa fa-eye", "enabled": false, "title": "Include Ignored Files"}, "showSearchDetails": false, "searchInWorkspaceOptions": {"matchCase": true, "matchWholeWord": true, "useRegExp": false, "includeIgnored": false, "include": [], "exclude": [], "maxResults": 2000}, "searchTerm": "", "replaceTerm": "", "showReplaceField": false}}, "rank": 200, "expanded": false}], "size": 291}, "rightPanel": {"type": "sidepanel", "items": []}, "activeWidgetId": "undefined:search-in-workspace"}